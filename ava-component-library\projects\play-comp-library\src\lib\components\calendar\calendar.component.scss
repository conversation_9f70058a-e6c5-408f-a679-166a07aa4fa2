$calendar-input-width-single: 16.5rem;
$calendar-input-width-range: 21.75rem;
$calendar-input-height: 3rem;

// Calendar Navigation
$calendar-nav-button-height-width: 2.25rem;
$calendar-nav-button-padding: var(--calendar-size-sm-padding);
$calendar-nav-controls-gap:  0.25rem;
$calendar-nav-controls-gap-large: var(--calendar-size-sm-padding);

// Calendar Input Segments
$calendar-segment-day-month-width: 1.5rem;
$calendar-segment-year-width: 2.5rem; 
$calendar-input-icon-right: 0.75rem;
$calendar-input-icon-padding: 0.375rem;

// Calendar Shadows
$calendar-nav-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.1);
$calendar-nav-shadow-hover: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.15);



.date-picker {
  font-family: var(--calendar-font-family);
  font-size: var(--calendar-size-sm-font);
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--calendar-size-sm-padding);
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.structured-input {
  display: flex;
  align-items: center;
  padding: var(--calendar-input-padding) 2.5rem var(--calendar-input-padding) var(--calendar-input-padding);
  border: var(--calendar-input-border);
  border-radius: var(--calendar-input-border-radius);
  background: var(--calendar-input-background);
  cursor: text;
  width: $calendar-input-width-single;
  height: $calendar-input-height;
  transition: all 0.2s ease;
  font-size: inherit;
}

.range-structured {
  width: $calendar-input-width-range;
  height: $calendar-input-height;
  gap: var(--calendar-size-sm-padding);
}

.date-part {
  display: flex;
  align-items: center;
}

.date-segment {
  border: none;
  outline: none;
  background: transparent;
  font-size: inherit;
  font-family: inherit;
  text-align: center;
  transition: background-color 0.2s ease;
}

.day-segment, .month-segment {
  width: $calendar-segment-day-month-width;
}

.year-segment {
  width: $calendar-segment-year-width;
}

.separator {
  color: var(--calendar-month-text);
  user-select: none;
}

.range-separator {
  color: var(--calendar-month-text);
  user-select: none;
}

.input-btn {
  position: absolute;
  right: $calendar-input-icon-right;
  background: var(--calendar-icon-background);
  border: none;
  cursor: pointer;
  padding: $calendar-input-icon-padding;
  border-radius: var(--calendar-icon-border-radius);
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: var(--calendar-nav-button-hover-background);

    ava-icon {
      color: var(--calendar-nav-button-text);
    }
  }

  ava-icon {
    color: var(--calendar-icon-color);
  }
}

.calendar-popup {
  position: absolute;
  top: calc(100% + 8px);
  z-index: var(--calendar-popup-z-index);
  background: var(--calendar-popup-background);
  border: var(--calendar-popup-border);
  border-radius: var(--calendar-popup-border-radius);
  box-shadow: var(--calendar-popup-shadow);
  padding: $calendar-input-icon-right;
  width: auto;
  height: auto;
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}



.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-controls {
  display: flex;
  gap: $calendar-nav-controls-gap-large;
}

.month-year-display {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.month-selector,
.year-selector {
  padding: 0.5rem 0;
  background: transparent;
  font-family: var(--calendar-font-family);
  font-size: var(--calendar-size-md-font);
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  border: none;
  outline: none;
}

.month-selector {
  color: var(--calendar-month-text);
}

.year-selector {
  color: var(--calendar-year-text);
}

.month-selector.selected {
  color: var(--calendar-month-text);
}

.year-selector.selected  {
  color: var(--calendar-month-text);
}

.month-selector:not(.selected) {
  color: var(--calendar-year-text);
}

.year-selector:not(.selected) {
  color: var(--calendar-year-text);
}

.month-selector:hover,
.year-selector:hover {
  opacity: 0.8;
}

.nav-controls {
  display: flex;
  gap: $calendar-nav-controls-gap;
}

.nav-btn {
  background: var(--calendar-nav-button-background);
  border: none;
  cursor: pointer;
  padding: $calendar-nav-button-padding;
  border-radius: 50%;
  transition: all 0.2s ease;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: $calendar-nav-button-height-width;
  height: $calendar-nav-button-height-width;
  box-shadow: $calendar-nav-shadow;

  &:hover {
    background: var(--calendar-nav-button-hover-background);
    box-shadow: $calendar-nav-shadow-hover;
  }
}

.calendar-grid {
  margin-top: 0.5rem;
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--calendar-size-sm-padding);
  margin-bottom: var(--calendar-size-sm-padding);
}

.weekday {
  height: auto;
  text-align: center;
  font-family: var(--calendar-font-family);
  font-size: var(--calendar-size-sm-font);
  color: var(--calendar-day-name-text);
  text-transform: uppercase;
  display: flex;
  align-items: center;
  justify-content: center;
}

.days {
  display: grid;
  text-align: center;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--calendar-size-sm-padding);
}

.day {
  height: auto;
  padding: var(--calendar-cell-padding);
  text-align: center;
  cursor: pointer;
  border: none;
  border-radius: var(--calendar-cell-border-radius);
  background: none;
  font-family: var(--calendar-font-family);
  font-size: var(--calendar-size-sm-font);
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--calendar-date-text);

  &:hover {
    background: var(--calendar-date-hover-background);
  }

  &.other-month {
    color: var(--calendar-date-disabled-text);
  }

  &.today {
    border: var(--calendar-date-today-border);
  }

  &.selected {
    background: var(--calendar-date-selected-background);
    color: var(--calendar-date-selected-text);
  }

  &.in-range {
    background: var(--calendar-range-start-background);
    color: var(--calendar-range-start-text);
  }

  &.range-start,
  &.range-end {
    background: var(--calendar-range-start-background);
    color: var(--calendar-range-start-text);
  }
}

/* Always Open / Embedded Calendar Styles */
.date-picker.always-open {
  .calendar-popup.embedded {
    position: static;
    box-shadow: var(--calendar-popup-shadow);
    border: var(--calendar-popup-border);
    border-radius: var(--calendar-popup-border-radius);
    background: var(--calendar-popup-background);
    z-index: auto;
    width: auto;
    min-width: auto;
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .calendar-popup {
    min-width: 280px;
    padding: 16px;
  }

  .structured-input {
    width: 100%;
  }

  .range-structured {
    width: 100%;
  }
}