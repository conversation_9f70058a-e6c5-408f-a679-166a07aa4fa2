<div class="main">
  <div class="layout">
    <div class="sidebar">
      <h2>Ava-Play</h2>
      <div class="theme-wrapper">
        <button [ngClass]="{ active: !active }" (click)="dark()">
          Darktheme
        </button>
        <button [ngClass]="{ active: active }" (click)="light()">
          Light Theme
        </button>
      </div>
      <div class="category-switcher" style="margin: 1rem 0">
        <div style="font-weight: bold; margin-bottom: 0.5rem">App Category</div>
        <button [ngClass]="{ active: appCategory === 'enterprise' }" (click)="setAppCategory('enterprise')">
          Enterprise
        </button>
        <button [ngClass]="{ active: appCategory === 'consumer' }" (click)="setAppCategory('consumer')">
          Consumer
        </button>
        <button [ngClass]="{ active: appCategory === 'marketing' }" (click)="setAppCategory('marketing')">
          Marketing
        </button>
      </div>
      <ul>
        <li><a routerLink="/app-button">Button</a></li>
        <li><a routerLink="/app-toggle">Toggle</a></li>
        <li><a routerLink="/app-checkbox">Checkbox</a></li>
        <li><a routerLink="/app-spinners">Spinners</a></li>
        <li><a routerLink="/app-icons">Icons</a></li>
        <li><a routerLink="/app-tabs">Tabs</a></li>
        <li><a routerLink="/app-textbox">Textbox</a></li>
        <li><a routerLink="/app-accordion">Accordion</a></li>
        <li><a routerLink="/app-badges">Badges</a></li>
        <li><a routerLink="/app-pagination">Pagination Controls</a></li>

        <li><a routerLink="/app-avatars">Avatars</a></li>
        <li><a routerLink="/app-cards">Cards</a></li>
        <li><a routerLink="/app-stepper">Stepper</a></li>
        <li><a routerLink="/app-textarea">Textarea</a></li>
        <li><a routerLink="/app-autocomplete">Autocomplete</a></li>
        <li><a routerLink="/app-dropdown">Dropdown</a></li>

        <li><a routerLink="/app-tags">Tags</a></li>
        <li><a routerLink="/app-sidebar">Sidebar</a></li>

        <li><a routerLink="/app-popup">Popup</a></li>
        <li><a routerLink="/app-date-input">Calendar</a></li>
         <li><a routerLink="/app-links">Links</a></li>
          <li><a routerLink="/app-table">Table</a></li>
          
        <li><a routerLink="/app-file-upload">File Upload</a></li>
        <li><a routerLink="/app-app-snackbar">Snackbar</a></li>

        <li><a routerLink="/app-slider">Slider</a></li>
        <li><a routerLink="/app-file-attach-pill">File Attach Pill</a></li>
         <li><a routerLink="/app-progress-bar">Progress bar</a></li>

      </ul>
    </div>
    <div class="content">
      <router-outlet></router-outlet>
      <ava-snackbar></ava-snackbar>
    </div>
  </div>
</div>