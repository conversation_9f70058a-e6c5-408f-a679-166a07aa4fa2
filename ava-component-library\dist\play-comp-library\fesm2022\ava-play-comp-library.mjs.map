{"version": 3, "file": "ava-play-comp-library.mjs", "sources": ["../../../projects/play-comp-library/src/lib/components/icon/icon.component.ts", "../../../projects/play-comp-library/src/lib/components/icon/icon.component.html", "../../../projects/play-comp-library/src/lib/components/button/button.component.ts", "../../../projects/play-comp-library/src/lib/components/button/button.component.html", "../../../projects/play-comp-library/src/lib/components/checkbox/checkbox.component.ts", "../../../projects/play-comp-library/src/lib/components/checkbox/checkbox.component.html", "../../../projects/play-comp-library/src/lib/components/toggle/toggle.component.ts", "../../../projects/play-comp-library/src/lib/components/toggle/toggle.component.html", "../../../projects/play-comp-library/src/lib/components/tabs/tabs.component.ts", "../../../projects/play-comp-library/src/lib/components/tabs/tabs.component.html", "../../../projects/play-comp-library/src/lib/components/pagination-controls/pagination-controls.component.ts", "../../../projects/play-comp-library/src/lib/components/pagination-controls/pagination-controls.component.html", "../../../projects/play-comp-library/src/lib/components/accordion/accordion.component.ts", "../../../projects/play-comp-library/src/lib/components/accordion/accordion.component.html", "../../../projects/play-comp-library/src/lib/components/textbox/ava-textbox.component.ts", "../../../projects/play-comp-library/src/lib/components/textbox/ava-textbox.component.html", "../../../projects/play-comp-library/src/lib/components/textarea/ava-textarea.component.ts", "../../../projects/play-comp-library/src/lib/components/textarea/ava-textarea.component.html", "../../../projects/play-comp-library/src/lib/components/badges/badges.component.ts", "../../../projects/play-comp-library/src/lib/components/badges/badges.component.html", "../../../projects/play-comp-library/src/lib/components/avatars/avatars.component.ts", "../../../projects/play-comp-library/src/lib/components/avatars/avatars.component.html", "../../../projects/play-comp-library/src/lib/components/spinner/spinner.component.ts", "../../../projects/play-comp-library/src/lib/components/spinner/spinner.component.html", "../../../projects/play-comp-library/src/lib/components/card/card.component.ts", "../../../projects/play-comp-library/src/lib/components/card/card.component.html", "../../../projects/play-comp-library/src/lib/components/feature-card/feature-card.component.ts", "../../../projects/play-comp-library/src/lib/components/feature-card/feature-card.component.html", "../../../projects/play-comp-library/src/lib/components/advanced-card/advanced-card.component.ts", "../../../projects/play-comp-library/src/lib/components/advanced-card/advanced-card.component.html", "../../../projects/play-comp-library/src/lib/components/popup/popup.component.ts", "../../../projects/play-comp-library/src/lib/components/popup/popup.component.html", "../../../projects/play-comp-library/src/lib/components/link/link.component.ts", "../../../projects/play-comp-library/src/lib/components/link/link.component.html", "../../../projects/play-comp-library/src/lib/components/tags/tags.component.ts", "../../../projects/play-comp-library/src/lib/components/tags/tags.component.html", "../../../projects/play-comp-library/src/lib/composite-components/approval-card/approval-card.component.ts", "../../../projects/play-comp-library/src/lib/composite-components/approval-card/approval-card.component.html", "../../../projects/play-comp-library/src/lib/composite-components/image-card/image-card.component.ts", "../../../projects/play-comp-library/src/lib/composite-components/image-card/image-card.component.html", "../../../projects/play-comp-library/src/lib/composite-components/text-card/text-card.component.ts", "../../../projects/play-comp-library/src/lib/composite-components/text-card/text-card.component.html", "../../../projects/play-comp-library/src/lib/components/dropdown/dropdown.component.ts", "../../../projects/play-comp-library/src/lib/components/dropdown/dropdown.component.html", "../../../projects/play-comp-library/src/lib/components/sidebar/sidebar.component.ts", "../../../projects/play-comp-library/src/lib/components/sidebar/sidebar.component.html", "../../../projects/play-comp-library/src/lib/components/slider/slider.component.ts", "../../../projects/play-comp-library/src/lib/components/slider/slider.component.html", "../../../projects/play-comp-library/src/lib/composite-components/confirmation-popup/confirmation-popup.component.ts", "../../../projects/play-comp-library/src/lib/composite-components/confirmation-popup/confirmation-popup.component.html", "../../../projects/play-comp-library/src/lib/constants/app.constants.ts", "../../../projects/play-comp-library/src/lib/components/fileupload/fileupload.component.ts", "../../../projects/play-comp-library/src/lib/components/fileupload/fileupload.component.html", "../../../projects/play-comp-library/src/lib/components/calendar/calendar.component.ts", "../../../projects/play-comp-library/src/lib/components/calendar/calendar.component.html", "../../../projects/play-comp-library/src/lib/components/file-attach-pill/file-attach-pill.component.ts", "../../../projects/play-comp-library/src/lib/components/file-attach-pill/file-attach-pill.component.html", "../../../projects/play-comp-library/src/lib/components/snackbar/snackbar.service.ts", "../../../projects/play-comp-library/src/lib/components/snackbar/snackbar.component.ts", "../../../projects/play-comp-library/src/lib/components/snackbar/snackbar.component.html", "../../../projects/play-comp-library/src/public-api.ts", "../../../projects/play-comp-library/src/ava-play-comp-library.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport {\r\n  ChangeDetectionStrategy, Component, Input, Output,\r\n  EventEmitter, ViewEncapsulation\r\n} from '@angular/core';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\n@Component({\r\n  selector: 'ava-icon',\r\n  imports: [CommonModule, LucideAngularModule],\r\n  templateUrl: './icon.component.html',\r\n  styleUrl: './icon.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n  encapsulation: ViewEncapsulation.None,\r\n  host: {\r\n    '[style.height.px]': 'iconSize',\r\n    'style': 'display: inline-flex; align-items: center; justify-content: center; vertical-align: middle;'\r\n  }\r\n})\r\nexport class IconComponent {\r\n  @Input() iconName = '';\r\n  @Input() color = '';\r\n  @Input() disabled = false;\r\n  @Input() iconColor = '#a1a1a1';\r\n  @Input() iconSize: number | string = 24;\r\n  @Input() cursor = false;\r\n  @Output() userClick = new EventEmitter<Event>();\r\n\r\n\r\n  get computedColor(): string {\r\n    if (this.disabled)\r\n      return 'var(--button-icon-color-disabled)';\r\n    return this.iconColor;\r\n  }\r\n  handleClick(event: Event): void {\r\n    if (this.disabled || !this.cursor) {\r\n      event.preventDefault();\r\n      return;\r\n    }\r\n    this.userClick.emit(event);\r\n  }\r\n}\r\n\r\n\r\n\r\n", "<button  class=\"ava-icon-container\" [ngClass] =\"{'disabled':disabled, 'cursor':cursor}\"  (click)=\"handleClick($event)\">\r\n    <lucide-icon [name]=\"iconName\" [size] = \"iconSize\"  [color] = \"computedColor\"></lucide-icon>\r\n</button> ", "import { CommonModule } from '@angular/common';\r\nimport {\r\n  Component,\r\n  Input,\r\n  ChangeDetectionStrategy,\r\n  ViewEncapsulation,\r\n  Output,\r\n  EventEmitter,\r\n} from '@angular/core';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport { IconComponent } from '../icon/icon.component';\r\n\r\n\r\nexport type ButtonVariant = 'primary' | 'secondary';\r\nexport type ButtonSize = 'small' | 'medium' | 'large' | 'normal';\r\nexport type ButtonState =\r\n  | 'default'\r\n  | 'active'\r\n  | 'disabled'\r\n  | 'danger'\r\n  | 'warning';\r\n\r\n@Component({\r\n  selector: 'ava-button',\r\n  standalone: true,\r\n  imports: [CommonModule, LucideAngularModule, IconComponent],\r\n  templateUrl: './button.component.html',\r\n  styleUrls: ['./button.component.scss',],\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class ButtonComponent {\r\n  @Input() label = '';\r\n  @Input() variant: ButtonVariant = 'primary';\r\n  @Input() size: ButtonSize = 'normal';\r\n  @Input() state: ButtonState = 'default';\r\n  @Input() visual: 'normal' | 'glass' | 'neo' = 'normal';\r\n  @Input() pill = false;\r\n  @Input() disabled = false;\r\n  @Input() width?: string;\r\n  @Input() height?: string;\r\n  @Input() gradient?: string;\r\n  @Input() background?: string;\r\n  @Input() color?: string;\r\n  @Input() dropdown = false;\r\n\r\n\r\n  @Input() iconName = '';\r\n  @Input() iconColor = '';\r\n  @Input() iconSize = 20;\r\n  @Input() iconPosition: 'left' | 'right' | 'only' = 'left';\r\n\r\n\r\n  @Output() userClick = new EventEmitter<Event>();\r\n\r\n\r\n  isActive = false;\r\n  timeoutRef: any;\r\n  basicOrAdvanced = 'basic';\r\n  ngOnInit() {\r\n    if (this.visual === 'neo') {\r\n      this.basicOrAdvanced = 'ava-button-advanced';\r\n    } else {\r\n      this.basicOrAdvanced = 'ava-button-basic';\r\n    }\r\n    this.isActive = this.state === 'active' ? true : false;\r\n  }\r\n  handleClick(event: Event): void {\r\n    if (this.disabled) {\r\n      event.preventDefault();\r\n      return;\r\n    }\r\n    this.setActiveState();\r\n    this.userClick.emit(event);\r\n  }\r\n\r\n  onKeydown(event: KeyboardEvent): void {\r\n    if (event.key === 'Enter' || event.key === ' ') {\r\n      event.preventDefault();\r\n      if (!this.disabled) {\r\n        this.setActiveState();\r\n        this.userClick.emit(event);\r\n      }\r\n    }\r\n  }\r\n  setActiveState(): void {\r\n    this.isActive = true;\r\n    this.timeoutRef = setTimeout(() => {\r\n      this.isActive = false;\r\n    }, 200);\r\n  }\r\n\r\n  get hasIcon(): boolean {\r\n    return !!this.iconName;\r\n  }\r\n\r\n  get computedIconColor(): string {\r\n    if (this.disabled)\r\n      return 'var(--button-icon-color-disabled)';\r\n    if (this.iconColor && this.isValidColor(this.iconColor))\r\n      return this.iconColor;\r\n    if (this.variant === 'primary')\r\n      return 'var(--button-primary-text)';\r\n    return 'var(--button-secondary-text)';\r\n  }\r\n\r\n  isValidColor(value: string): boolean {\r\n    const s = new Option().style;\r\n    s.color = value;\r\n    return s.color !== '';\r\n  }\r\n\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.timeoutRef) {\r\n      clearTimeout(this.timeoutRef);\r\n    }\r\n  }\r\n}\r\n", "<button class=\"ava-button\" [class]=\"\r\n    [\r\n      basicOrAdvanced,\r\n      variant,\r\n      size,\r\n      state,\r\n      pill === true ? 'pill' : '',\r\n      state === 'active' ? 'active' : '',\r\n      isActive ? 'active-anim' : ''\r\n    ].join(' ')\r\n  \" [style.width]=\"width ? width : null\" [style.height.px]=\"height ? height : null\"\r\n  [style.background]=\"gradient || background || null\" [style.color]=\"color ? color : null\" [disabled]=\"disabled\"\r\n  [ngClass]=\"[\r\n    visual,\r\n    iconPosition === 'only' ? 'icon-only-ava-button' : '',\r\n    iconPosition === 'left' || iconPosition === 'right'\r\n      ? 'icon-lr-ava-button'\r\n      : '',\r\n    gradient ? 'gradient' : ''\r\n  ]\" (click)=\"handleClick($event)\" (keydown)=\"onKeydown($event)\">\r\n  <ng-container *ngIf=\"(iconName && iconPosition === 'left') || iconPosition === 'only'\">\r\n    <ava-icon [iconName]=\"iconName\" [iconColor]=\"computedIconColor\" [iconSize]=\"iconSize\"\r\n      [disabled]=\"disabled\"></ava-icon>\r\n  </ng-container>\r\n  <span *ngIf=\"iconPosition !== 'only'\" class=\"ava-button__label\">{{\r\n    label\r\n    }}</span>\r\n  <ng-container *ngIf=\"iconName && iconPosition === 'right'\">\r\n    <ava-icon [iconName]=\"iconName\" [iconColor]=\"computedIconColor\" [iconSize]=\"iconSize\"\r\n      [disabled]=\"disabled\"></ava-icon>\r\n  </ng-container>\r\n\r\n  <ava-icon class=\"b-dropdown\" *ngIf=\"dropdown\" iconName=\"chevron-down\" [iconColor]=\"computedIconColor\"\r\n    [iconSize]=\"30\"></ava-icon>\r\n</button>", "import { CommonModule } from '@angular/common';\r\nimport { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, HostListener} from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'ava-checkbox',\r\n  imports: [CommonModule],\r\n  templateUrl: './checkbox.component.html',\r\n  styleUrl: './checkbox.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class CheckboxComponent {\r\n  @Input() variant: 'default' | 'with-bg' | 'animated' = 'default';\r\n  @Input() size: 'small' | 'medium' | 'large' = 'medium';\r\n  @Input() label: string = '';\r\n  @Input() isChecked: boolean = false;\r\n  @Input() indeterminate: boolean = false;\r\n  @Input() disable: boolean = false;\r\n  \r\n  @Output() isCheckedChange = new EventEmitter<boolean>();\r\n\r\n  isAnimating: boolean = false;\r\n  isUnchecking: boolean = false;\r\n\r\n  // Getter for container classes\r\n  get containerClasses(): Record<string, boolean> {\r\n    return {\r\n      'with-bg': this.variant === 'with-bg',\r\n      'animated': this.variant === 'animated',\r\n      'small': this.size === 'small',\r\n      'medium': this.size === 'medium',\r\n      'large': this.size === 'large',\r\n      'disabled': this.disable,\r\n    };\r\n  }\r\n\r\n  // Getter for checkbox classes\r\n  get checkboxClasses(): Record<string, boolean> {\r\n    return {\r\n      'checked': this.isChecked && !this.isUnchecking,\r\n      'indeterminate': this.indeterminate,\r\n      'checking': this.isAnimating && this.isChecked,\r\n      'unchecking': this.isUnchecking\r\n    };\r\n  }\r\n\r\n  // Getter for showing icon\r\n  get showIcon(): boolean {\r\n    return this.isChecked || this.indeterminate || this.isUnchecking;\r\n  }\r\n\r\n  // Getter for showing checkmark\r\n  get showCheckmark(): boolean {\r\n    return (this.isChecked || this.isUnchecking) && !this.indeterminate;\r\n  }\r\n\r\n  toggleCheckbox(): void {\r\n    if (this.disable) return;\r\n\r\n    if (this.indeterminate) {\r\n      this.isChecked = true;\r\n      this.indeterminate = false;\r\n      this.isCheckedChange.emit(this.isChecked);\r\n      return;\r\n    }\r\n\r\n    if (this.variant === 'animated') {\r\n      if (this.isChecked) {\r\n        this.handleUnchecking();\r\n      } else {\r\n        this.handleChecking();\r\n      }\r\n    } else if (this.variant === 'with-bg') {\r\n      if (this.isChecked) {\r\n        this.handleWithBgUnchecking();\r\n      } else {\r\n        this.isChecked = true;\r\n        this.isCheckedChange.emit(this.isChecked);\r\n      }\r\n    } else {\r\n      // Default variant\r\n      if (this.isChecked) {\r\n        this.isUnchecking = true;\r\n\r\n        setTimeout(() => {\r\n          this.isChecked = false;\r\n          this.isUnchecking = false;\r\n          this.isCheckedChange.emit(this.isChecked);\r\n        }, 300); // Wait for erase animation (300ms)\r\n      } else {\r\n        this.isChecked = true;\r\n        this.isCheckedChange.emit(this.isChecked);\r\n      }\r\n    }\r\n  }\r\n\r\n  // Keyboard accessibility\r\n  onKeyDown(event: KeyboardEvent): void {\r\n    // Handle Space and Enter keys to toggle checkbox\r\n    if (event.key === ' ' || event.key === 'Enter') {\r\n      event.preventDefault(); // Prevent default scrolling behavior for Space\r\n      this.toggleCheckbox();\r\n    }\r\n  }\r\n\r\n  private handleChecking(): void {\r\n    this.isAnimating = true;\r\n    this.isChecked = true;\r\n    \r\n    setTimeout(() => {\r\n      this.isAnimating = false;\r\n      this.isCheckedChange.emit(this.isChecked);\r\n    }, 600); // Background fill (300ms) + checkmark draw (150ms) + delay (300ms)\r\n  }\r\n\r\n  private handleUnchecking(): void {\r\n    this.isUnchecking = true;\r\n    \r\n    setTimeout(() => {\r\n      this.isChecked = false;\r\n      this.isUnchecking = false;\r\n      this.isCheckedChange.emit(this.isChecked);\r\n    }, 300); // Both background empty and checkmark erase (300ms)\r\n  }\r\n\r\n  private handleWithBgUnchecking(): void {\r\n    this.isUnchecking = true;\r\n    \r\n    setTimeout(() => {\r\n      this.isChecked = false;\r\n      this.isUnchecking = false;\r\n      this.isCheckedChange.emit(this.isChecked);\r\n    }, 150); // Both background transition and checkmark erase (150ms)\r\n  }\r\n}", "<div class=\"ava-checkbox\"\r\n     [ngClass]=\"containerClasses\"\r\n     (click)=\"toggleCheckbox()\"\r\n     (keydown)=\"onKeyDown($event)\"\r\n     [tabindex]=\"disable ? -1 : 0\"\r\n     [attr.role]=\"'checkbox'\"\r\n     [attr.aria-checked]=\"indeterminate ? 'mixed' : isChecked\"\r\n     [attr.aria-disabled]=\"disable\"\r\n     [attr.aria-label]=\"label || 'Checkbox'\">\r\n  <div class=\"checkbox\" [ngClass]=\"checkboxClasses\">\r\n    @if (showIcon) {\r\n    <svg class=\"checkbox-icon\" viewBox=\"0 0 24 24\">\r\n      @if (showCheckmark) {\r\n      <path class=\"checkmark-path\" [class.unchecking]=\"isUnchecking\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"\r\n        stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M5 12l5 5L20 7\" />\r\n      }\r\n      @if (indeterminate) {\r\n      <rect class=\"indeterminate-rect\" fill=\"currentColor\" x=\"6\" y=\"10\" width=\"12\" height=\"4\" rx=\"2\" />\r\n      }\r\n    </svg>\r\n    }\r\n  </div>\r\n  @if (label) {\r\n  <span class=\"checkbox-label\">{{ label }}</span>\r\n  }\r\n</div>", "// toggle.component.ts\r\nimport { Component, ChangeDetectionStrategy, Input, Output, EventEmitter } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nexport type ToggleSize = 'small' | 'medium' | 'large';\r\nexport type TogglePosition = 'left' | 'right';\r\n\r\n@Component({\r\n  selector: 'ava-toggle',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './toggle.component.html',\r\n  styleUrls: ['./toggle.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n})\r\nexport class ToggleComponent {\r\n  @Input() size: ToggleSize = 'medium';\r\n  @Input() title: string = '';\r\n  @Input() position: TogglePosition = 'left';\r\n  @Input() disabled: boolean = false;\r\n  @Input() checked: boolean = false;\r\n  @Input() animation: boolean = true;\r\n  \r\n  @Output() checkedChange = new EventEmitter<boolean>();\r\n\r\n  onToggle(): void {\r\n    if (this.disabled) return;\r\n    \r\n    this.checked = !this.checked;\r\n    this.checkedChange.emit(this.checked);\r\n  }\r\n\r\n  onKeyDown(event: KeyboardEvent): void {\r\n    if (event.key === ' ' || event.key === 'Enter') {\r\n      event.preventDefault();\r\n      this.onToggle();\r\n    }\r\n  }\r\n\r\n  get titleName(): string | null {\r\n    return this.title \r\n      ? `toggle-title-${this.title.replace(/\\s+/g, '-').toLowerCase()}` \r\n      : null;\r\n  }\r\n}", "<!-- toggle.component.html -->\r\n<div class=\"ava-toggle-container\"\r\n     [class.toggle-left]=\"position === 'left'\"\r\n     [class.toggle-right]=\"position === 'right'\"\r\n     [class.disabled]=\"disabled\">\r\n  \r\n  <!-- Title - always show when it exists, let CSS handle positioning -->\r\n  <span *ngIf=\"title\"\r\n        class=\"toggle-title\"\r\n        [class.disabled]=\"disabled\"\r\n        [id]=\"titleName\">\r\n    {{ title }}\r\n  </span>\r\n  \r\n  <!-- Toggle Switch -->\r\n  <div class=\"toggle-wrapper\"\r\n       [class.toggle-small]=\"size === 'small'\"\r\n       [class.toggle-medium]=\"size === 'medium'\"\r\n       [class.toggle-large]=\"size === 'large'\"\r\n       [class.checked]=\"checked\"\r\n       [class.disabled]=\"disabled\"\r\n       [class.animated]=\"animation\"\r\n       [tabindex]=\"disabled ? -1 : 0\"\r\n       [attr.role]=\"'switch'\"\r\n       [attr.aria-checked]=\"checked\"\r\n       [attr.aria-disabled]=\"disabled\"\r\n       [attr.aria-labelledby]=\"titleName\"\r\n       [attr.aria-label]=\"!title ? 'Toggle switch' : null\"\r\n       (click)=\"onToggle()\"\r\n       (keydown)=\"onKeyDown($event)\">\r\n    \r\n    <div class=\"toggle-slider\"></div>\r\n    \r\n    <!-- Screen reader support -->\r\n    <span class=\"sr-only\">\r\n      {{ title || 'Toggle switch' }} {{ checked ? 'enabled' : 'disabled' }}\r\n    </span>\r\n  </div>\r\n</div>", "import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, ViewEncapsulation, OnInit, HostBinding, ViewChild, ElementRef, AfterViewInit, QueryList, ViewChildren, OnDestroy, ChangeDetectorRef, AfterViewChecked } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport { IconComponent } from '../icon/icon.component';\r\nimport { ButtonComponent } from '../button/button.component';\r\n\r\nexport interface AvaTabDropdownItem {\r\n  label: string;\r\n  value: string | number;\r\n  icon?: string;\r\n  iconColor?: string;\r\n  subtitle?: string;\r\n}\r\n\r\nexport interface AvaTab {\r\n  label: string;\r\n  value: string | number;\r\n  icon?: string; // Lucide icon name\r\n  iconPosition?: 'top' | 'bottom' | 'start' | 'end';\r\n  disabled?: boolean;\r\n  content?: string | null;\r\n  id?: string;\r\n  dropdown?: {\r\n    items: AvaTabDropdownItem[];\r\n  };\r\n  iconColor?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ava-tabs',\r\n  standalone: true,\r\n  imports: [CommonModule, LucideAngularModule, IconComponent, ButtonComponent],\r\n  templateUrl: './tabs.component.html',\r\n  styleUrls: ['./tabs.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class TabsComponent implements OnInit, AfterViewInit, AfterViewChecked, OnDestroy {\r\n  @Input() tabs: AvaTab[] = [];\r\n  @Input() value: string | number | null = null;\r\n  @Output() valueChange = new EventEmitter<string | number>();\r\n  @Input() highlightActiveText = true;\r\n  @Input() maxWidth?: string;\r\n  @Input() showChevrons = false;\r\n  @Input() ariaLabel?: string;\r\n  @Input() variant: 'default' | 'button' | 'icon' = 'default';\r\n  @Input() style?: Record<string, string>;\r\n  @Input() iconColor?: string;\r\n  @Input() container = false;\r\n  @Input() containerStyle?: Record<string, string>;\r\n  @Output() dropdownSelect = new EventEmitter<{ parent: AvaTab; item: { label: string; value: string | number; icon?: string } }>();\r\n  @Output() tabClick = new EventEmitter<AvaTab>();\r\n  @Output() tabHover = new EventEmitter<AvaTab>();\r\n  @Output() tabFocus = new EventEmitter<AvaTab>();\r\n  @Output() tabBlur = new EventEmitter<AvaTab>();\r\n  @Output() dropdownItemClick = new EventEmitter<{ parent: AvaTab; item: { label: string; value: string | number; icon?: string } }>();\r\n  @Output() dropdownItemHover = new EventEmitter<{ parent: AvaTab; item: { label: string; value: string | number; icon?: string } }>();\r\n  @Output() dropdownItemFocus = new EventEmitter<{ parent: AvaTab; item: { label: string; value: string | number; icon?: string } }>();\r\n  @Output() dropdownItemBlur = new EventEmitter<{ parent: AvaTab; item: { label: string; value: string | number; icon?: string } }>();\r\n  /**\r\n   * Props to pass to ava-button when using the 'button' variant. All ava-button @Inputs are supported.\r\n   * Defaults to { variant: 'secondary' } for design system consistency.\r\n   */\r\n  @Input() buttonProps: Partial<ButtonComponent> = { variant: 'secondary' };\r\n  /**\r\n   * Style object for the tab list (nav.ava-tabs__list). Useful for glassmorphic, neomorphic, or custom backgrounds.\r\n   */\r\n  @Input() listStyle?: Record<string, string>;\r\n  /**\r\n   * Style object for the wrapper (div.awe-tabs__container). Useful for custom backgrounds or effects when container is false.\r\n   */\r\n  @Input() wrapperStyle?: Record<string, string>;\r\n  /**\r\n   * Optional style object for the dropdown menu (ava-tabs__dropdown-menu). Allows full customization.\r\n   */\r\n  @Input() dropdownMenuStyle?: Record<string, string>;\r\n\r\n  @HostBinding('style.--tabs-count')\r\n  get tabsCount() {\r\n    return this.tabs.length;\r\n  }\r\n\r\n  @ViewChild('tabList') tabList!: ElementRef<HTMLElement>;\r\n  @ViewChildren('tabButton') tabButtons!: QueryList<ElementRef<HTMLButtonElement>>;\r\n\r\n  underline = { width: 0, left: 0 };\r\n  showScrollButtons = false;\r\n  disableScrollLeft = true;\r\n  disableScrollRight = false;\r\n  openDropdownIndex: number | null = null;\r\n  isTabHovered: number | null = null;\r\n  isDropdownHovered: number | null = null;\r\n  dropdownPosition: { left: number; top: number } | null = null;\r\n\r\n  private resizeObserver?: ResizeObserver;\r\n  private lastTabsSnapshot = '';\r\n  private lastValue: string | number | null = null;\r\n  private initialized = false;\r\n  private dropdownCloseTimeout: ReturnType<typeof setTimeout> | null = null;\r\n  private tabButtonRefs: HTMLElement[] = [];\r\n  private dropdownMenuRef: HTMLElement | null = null;\r\n  private justOpenedDropdown = false;\r\n\r\n  constructor(private cdr: ChangeDetectorRef) {}\r\n\r\n  ngOnInit() {\r\n    if (this.tabs.length && (this.value === null || !this.tabs.some(tab => tab.value === this.value))) {\r\n      this.value = this.tabs[0]?.value;\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    // Initial microtask deferral for DOM paint\r\n    Promise.resolve().then(() => {\r\n      this.initializeComponent();\r\n      this.initialized = true;\r\n    });\r\n    // Track tab button refs for outside click detection\r\n    setTimeout(() => {\r\n      this.tabButtonRefs = this.tabButtons.map(ref => ref.nativeElement);\r\n    });\r\n  }\r\n\r\n  ngAfterViewChecked() {\r\n    // Only recalculate if tabs or value changed\r\n    const tabsSnapshot = JSON.stringify(this.tabs.map(t => ({ label: t.label, value: t.value, icon: t.icon, iconPosition: t.iconPosition, disabled: t.disabled })));\r\n    if (tabsSnapshot !== this.lastTabsSnapshot || this.value !== this.lastValue) {\r\n      this.lastTabsSnapshot = tabsSnapshot;\r\n      this.lastValue = this.value;\r\n      Promise.resolve().then(() => {\r\n        this.initializeComponent();\r\n      });\r\n    }\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.resizeObserver?.disconnect();\r\n    this.removeDocumentClickListener();\r\n  }\r\n\r\n  private initializeComponent() {\r\n    this.checkForOverflow();\r\n    this.updateUnderlinePosition();\r\n    this.resizeObserver = new ResizeObserver(() => {\r\n      this.checkForOverflow();\r\n      this.updateUnderlinePosition();\r\n    });\r\n    this.resizeObserver.observe(this.tabList.nativeElement);\r\n  }\r\n\r\n  public onTabClick(tab: AvaTab, event?: Event) {\r\n    const idx = this.tabs.findIndex(t => t.value === tab.value);\r\n    if (tab.dropdown) {\r\n      if (this.openDropdownIndex === idx) {\r\n        this.openDropdownIndex = null;\r\n        this.dropdownPosition = null;\r\n        this.isTabHovered = null;\r\n        this.isDropdownHovered = null;\r\n        this.removeDocumentClickListener();\r\n        return;\r\n      } else {\r\n        this.openDropdownIndex = idx;\r\n        if (event && event.currentTarget) {\r\n          const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();\r\n          const scrollY = window.scrollY || window.pageYOffset;\r\n          this.dropdownPosition = {\r\n            left: rect.left + rect.width / 2,\r\n            top: rect.bottom + scrollY\r\n          };\r\n        } else {\r\n          this.dropdownPosition = { left: 0, top: 48 };\r\n        }\r\n        this.justOpenedDropdown = true;\r\n        this.addDocumentClickListener();\r\n        return;\r\n      }\r\n    }\r\n    if (!tab.disabled && tab.value !== this.value) {\r\n      this.value = tab.value;\r\n      this.updateUnderlinePosition();\r\n      this.valueChange.emit(tab.value);\r\n    }\r\n    this.tabClick.emit(tab);\r\n  }\r\n\r\n  public onTabHover(tab: AvaTab) {\r\n    this.tabHover.emit(tab);\r\n  }\r\n\r\n  public onTabFocus(tab: AvaTab) {\r\n    this.tabFocus.emit(tab);\r\n  }\r\n\r\n  public onTabBlur(tab: AvaTab) {\r\n    this.tabBlur.emit(tab);\r\n  }\r\n\r\n  public scroll(direction: 'left' | 'right'): void {\r\n    const scrollAmount = direction === 'left' ? -200 : 200;\r\n    this.tabList.nativeElement.scrollBy({ left: scrollAmount, behavior: 'smooth' });\r\n    setTimeout(() => {\r\n      this.updateScrollButtonState();\r\n    }, 300);\r\n  }\r\n\r\n  private checkForOverflow() {\r\n    if (!this.tabList) return;\r\n    const el = this.tabList.nativeElement;\r\n    const hasOverflow = el.scrollWidth > el.clientWidth;\r\n    this.showScrollButtons = this.showChevrons && hasOverflow;\r\n    this.updateScrollButtonState();\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  public updateScrollButtonState(): void {\r\n    if (!this.tabList) return;\r\n    const el = this.tabList.nativeElement;\r\n    this.disableScrollLeft = el.scrollLeft === 0;\r\n    this.disableScrollRight = el.scrollLeft + el.clientWidth >= el.scrollWidth - 1;\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private updateUnderlinePosition() {\r\n    if (!this.tabButtons || this.tabButtons.length === 0 || !this.tabList) return;\r\n    const idx = this.tabs.findIndex(tab => tab.value === this.value);\r\n    if (idx === -1) return;\r\n    const tabElement = this.tabButtons.get(idx)?.nativeElement;\r\n    if (tabElement) {\r\n      const navRect = this.tabList.nativeElement.getBoundingClientRect();\r\n      const tabRect = tabElement.getBoundingClientRect();\r\n      this.underline.width = tabRect.width;\r\n      this.underline.left = tabRect.left - navRect.left + this.tabList.nativeElement.scrollLeft;\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  get activeTab(): AvaTab | undefined {\r\n    return this.tabs.find(tab => tab.value === this.value);\r\n  }\r\n\r\n  public onDropdownItemClick(tab: AvaTab, item: { label: string; value: string | number; icon?: string }) {\r\n    this.value = tab.value;\r\n    this.valueChange.emit(tab.value);\r\n    this.dropdownSelect.emit({ parent: tab, item });\r\n    this.dropdownItemClick.emit({ parent: tab, item });\r\n    this.openDropdownIndex = null;\r\n  }\r\n\r\n  public onDropdownItemHover(tab: AvaTab, item: { label: string; value: string | number; icon?: string }) {\r\n    this.dropdownItemHover.emit({ parent: tab, item });\r\n  }\r\n\r\n  public onDropdownItemFocus(tab: AvaTab, item: { label: string; value: string | number; icon?: string }) {\r\n    this.dropdownItemFocus.emit({ parent: tab, item });\r\n  }\r\n\r\n  public onDropdownItemBlur(tab: AvaTab, item: { label: string; value: string | number; icon?: string }) {\r\n    this.dropdownItemBlur.emit({ parent: tab, item });\r\n  }\r\n\r\n  public onTabDropdownEnter(i: number, tabButton?: HTMLElement) {\r\n    this.isTabHovered = i;\r\n    this.openDropdownIndex = i;\r\n    if (this.dropdownCloseTimeout) {\r\n      clearTimeout(this.dropdownCloseTimeout);\r\n      this.dropdownCloseTimeout = null;\r\n    }\r\n    if (tabButton) {\r\n      const rect = tabButton.getBoundingClientRect();\r\n      const scrollY = window.scrollY || window.pageYOffset;\r\n      this.dropdownPosition = {\r\n        left: rect.left + rect.width / 2,\r\n        top: rect.bottom + scrollY\r\n      };\r\n    }\r\n    this.addDocumentClickListener();\r\n  }\r\n\r\n  public onTabDropdownLeave(i: number) {\r\n    this.isTabHovered = null;\r\n    if (this.dropdownCloseTimeout) {\r\n      clearTimeout(this.dropdownCloseTimeout);\r\n      this.dropdownCloseTimeout = null;\r\n    }\r\n    this.dropdownCloseTimeout = setTimeout(() => {\r\n      if (this.isTabHovered !== i && this.isDropdownHovered !== i) {\r\n        this.openDropdownIndex = null;\r\n        this.dropdownPosition = null;\r\n      }\r\n    }, 180);\r\n  }\r\n\r\n  public onDropdownMenuEnter(i: number, ref?: HTMLElement) {\r\n    this.isDropdownHovered = i;\r\n    this.openDropdownIndex = i;\r\n    if (this.dropdownCloseTimeout) {\r\n      clearTimeout(this.dropdownCloseTimeout);\r\n      this.dropdownCloseTimeout = null;\r\n    }\r\n    if (ref) {\r\n      this.dropdownMenuRef = ref;\r\n    }\r\n  }\r\n\r\n  public onDropdownMenuLeave(i: number) {\r\n    this.isDropdownHovered = null;\r\n    if (this.dropdownCloseTimeout) {\r\n      clearTimeout(this.dropdownCloseTimeout);\r\n      this.dropdownCloseTimeout = null;\r\n    }\r\n    this.dropdownCloseTimeout = setTimeout(() => {\r\n      if (this.isTabHovered !== i && this.isDropdownHovered !== i) {\r\n        this.openDropdownIndex = null;\r\n        this.dropdownPosition = null;\r\n      }\r\n    }, 180);\r\n  }\r\n\r\n  private addDocumentClickListener() {\r\n    document.addEventListener('mousedown', this.handleDocumentClick, true);\r\n  }\r\n\r\n  private removeDocumentClickListener() {\r\n    document.removeEventListener('mousedown', this.handleDocumentClick, true);\r\n  }\r\n\r\n  private handleDocumentClick = (event: MouseEvent) => {\r\n    if (this.justOpenedDropdown) {\r\n      this.justOpenedDropdown = false;\r\n      return;\r\n    }\r\n    if (!this.dropdownMenuRef && this.openDropdownIndex !== null) return;\r\n    const dropdownMenu = this.dropdownMenuRef;\r\n    const tabButton = this.tabButtonRefs[this.openDropdownIndex!];\r\n    if (\r\n      dropdownMenu &&\r\n      !dropdownMenu.contains(event.target as Node) &&\r\n      tabButton &&\r\n      !tabButton.contains(event.target as Node)\r\n    ) {\r\n      this.openDropdownIndex = null;\r\n      this.dropdownPosition = null;\r\n      this.isTabHovered = null;\r\n      this.isDropdownHovered = null;\r\n      this.removeDocumentClickListener();\r\n    }\r\n  };\r\n\r\n  get customStyle() {\r\n    return {\r\n      ...(this.style || {}),\r\n      ...(this.maxWidth ? { 'max-width': this.maxWidth, width: '100%' } : {})\r\n    };\r\n  }\r\n} ", "<ng-container *ngIf=\"container; else noContainer\">\r\n  <div class=\"ava-tabs__container-wrapper\" [ngStyle]=\"containerStyle\">\r\n    <div class=\"awe-tabs__container\" [ngStyle]=\"wrapperStyle ? wrapperStyle : customStyle\">\r\n      <div class=\"awe-tabs__scroll-area\">\r\n        <nav #tabList class=\"ava-tabs__list\" [ngStyle]=\"listStyle\"\r\n          [class.awe-tabs--highlight-active]=\"highlightActiveText\" role=\"tablist\" [attr.aria-label]=\"ariaLabel\"\r\n          (scroll)=\"updateScrollButtonState()\">\r\n          <ng-container *ngIf=\"variant === 'button'; else notButtonVariant\">\r\n            <ava-button *ngFor=\"let tab of tabs; let i = index\" class=\"ava-tabs__tab\" [ngClass]=\"{\r\n                'ava-tabs__tab--active': tab.value === value,\r\n                'ava-tabs__tab--disabled': !!tab.disabled,\r\n                'ava-tabs__tab--has-dropdown': !!tab.dropdown\r\n              }\" [label]=\"tab.label\" [iconName]=\"tab.icon || ''\" [iconPosition]=\"\r\n                tab.iconPosition === 'end'\r\n                  ? 'right'\r\n                  : tab.iconPosition === 'start'\r\n                  ? 'left'\r\n                  : 'left'\r\n              \" [disabled]=\"!!tab.disabled\" [attr.aria-selected]=\"tab.value === value\"\r\n              [attr.aria-disabled]=\"tab.disabled\" [attr.tabindex]=\"tab.disabled ? -1 : 0\" [attr.aria-label]=\"tab.label\"\r\n              [attr.title]=\"tab.label\" (userClick)=\"onTabClick(tab, $event)\" (mouseenter)=\"\r\n                tab.dropdown ? onTabDropdownEnter(i) : null; onTabHover(tab)\r\n              \" (mouseleave)=\"\r\n                tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n              \" (focusin)=\"\r\n                tab.dropdown ? onTabDropdownEnter(i) : null; onTabFocus(tab)\r\n              \" (focusout)=\"\r\n                tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n              \" [variant]=\"buttonProps.variant || 'secondary'\" [size]=\"buttonProps.size || 'normal'\" [state]=\"\r\n                tab.value === value ? 'active' : buttonProps.state || 'default'\r\n              \" [visual]=\"buttonProps.visual || 'normal'\" [pill]=\"buttonProps.pill ?? false\"\r\n              [width]=\"buttonProps.width || ''\" [height]=\"buttonProps.height || ''\"\r\n              [gradient]=\"buttonProps.gradient || ''\" [background]=\"buttonProps.background || ''\"\r\n              [color]=\"buttonProps.color || ''\" [iconColor]=\"buttonProps.iconColor || ''\"\r\n              [iconSize]=\"buttonProps.iconSize ?? 18\">\r\n              <ng-container *ngIf=\"tab.dropdown\">\r\n                <span class=\"ava-tabs__dropdown-chevron\" aria-hidden=\"true\">▼</span>\r\n              </ng-container>\r\n            </ava-button>\r\n          </ng-container>\r\n          <ng-template #notButtonVariant>\r\n            <button *ngFor=\"let tab of tabs; let i = index\" #tabButton class=\"ava-tabs__tab\" [ngClass]=\"{\r\n                'icon-top': tab.iconPosition === 'top',\r\n                'icon-bottom': tab.iconPosition === 'bottom',\r\n                'ava-tabs__tab--has-dropdown': tab.dropdown\r\n              }\" [class.ava-tabs__tab--active]=\"tab.value === value\" [class.ava-tabs__tab--disabled]=\"tab.disabled\"\r\n              [attr.aria-selected]=\"tab.value === value\" [attr.aria-disabled]=\"tab.disabled\"\r\n              [attr.tabindex]=\"tab.disabled ? -1 : 0\" [attr.aria-label]=\"variant === 'icon' ? tab.label : null\"\r\n              [attr.title]=\"variant === 'icon' ? tab.label : null\" (click)=\"onTabClick(tab)\" (mouseenter)=\"\r\n                tab.dropdown ? onTabDropdownEnter(i, tabButton) : null;\r\n                onTabHover(tab)\r\n              \" (mouseleave)=\"\r\n                tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n              \" (focusin)=\"\r\n                tab.dropdown ? onTabDropdownEnter(i, tabButton) : null;\r\n                onTabFocus(tab)\r\n              \" (focusout)=\"\r\n                tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n              \" type=\"button\" role=\"tab\">\r\n              <ng-container *ngIf=\"variant === 'icon'; else normalTabContent\">\r\n                <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\" [iconColor]=\"'var(--tab-icon-color)'\"\r\n                  [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n              </ng-container>\r\n              <ng-template #normalTabContent>\r\n                <ng-container [ngSwitch]=\"tab.iconPosition\">\r\n                  <ng-container *ngSwitchCase=\"'top'\">\r\n                    <div class=\"ava-tabs__icon-top\">\r\n                      <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                        [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                    </div>\r\n                    <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                  </ng-container>\r\n                  <ng-container *ngSwitchCase=\"'bottom'\">\r\n                    <div class=\"ava-tabs__icon-bottom\">\r\n                      <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                        [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                    </div>\r\n                    <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                  </ng-container>\r\n                  <ng-container *ngSwitchCase=\"'start'\">\r\n                    <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                      [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                    <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                  </ng-container>\r\n                  <ng-container *ngSwitchCase=\"'end'\">\r\n                    <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                    <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                      [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"\r\n                      class=\"ava-tabs__icon\"></ava-icon>\r\n                  </ng-container>\r\n                  <ng-container *ngSwitchDefault>\r\n                    <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                  </ng-container>\r\n                </ng-container>\r\n                <ng-container *ngIf=\"tab.dropdown\">\r\n                  <span class=\"ava-tabs__dropdown-arrow\" aria-hidden=\"true\">▼</span>\r\n                </ng-container>\r\n              </ng-template>\r\n            </button>\r\n          </ng-template>\r\n          <div *ngIf=\"variant !== 'button' && variant !== 'icon'\" class=\"ava-tabs__underline\"\r\n            [style.width.px]=\"underline.width\" [style.transform]=\"'translateX(' + underline.left + 'px)'\"></div>\r\n        </nav>\r\n        <button *ngIf=\"showScrollButtons\" class=\"awe-tabs__scroll-btn awe-tabs__scroll-btn--left\"\r\n          (click)=\"scroll('left')\" [disabled]=\"disableScrollLeft\">\r\n          <ava-icon iconName=\"chevron-left\" [iconSize]=\"18\" [iconColor]=\"'grey'\" [cursor]=\"false\"></ava-icon>\r\n        </button>\r\n        <button *ngIf=\"showScrollButtons\" class=\"awe-tabs__scroll-btn awe-tabs__scroll-btn--right\"\r\n          (click)=\"scroll('right')\" [disabled]=\"disableScrollRight\">\r\n          <ava-icon iconName=\"chevron-right\" [iconSize]=\"18\" [iconColor]=\"'grey'\" [cursor]=\"false\"></ava-icon>\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"ava-tabs__content\" *ngIf=\"activeTab?.content\">\r\n      {{ activeTab?.content }}\r\n    </div>\r\n  </div>\r\n</ng-container>\r\n<ng-template #noContainer>\r\n  <div class=\"awe-tabs__container\" [ngStyle]=\"wrapperStyle ? wrapperStyle : customStyle\">\r\n    <div class=\"awe-tabs__scroll-area\">\r\n      <nav #tabList class=\"ava-tabs__list\" [ngStyle]=\"listStyle\"\r\n        [class.awe-tabs--highlight-active]=\"highlightActiveText\" role=\"tablist\" [attr.aria-label]=\"ariaLabel\"\r\n        (scroll)=\"updateScrollButtonState()\">\r\n        <ng-container *ngIf=\"variant === 'button'; else notButtonVariant\">\r\n          <ava-button *ngFor=\"let tab of tabs; let i = index\" class=\"ava-tabs__tab\" [ngClass]=\"{\r\n              'ava-tabs__tab--active': tab.value === value,\r\n              'ava-tabs__tab--disabled': !!tab.disabled,\r\n              'ava-tabs__tab--has-dropdown': !!tab.dropdown\r\n            }\" [label]=\"tab.label\" [iconName]=\"tab.icon || ''\" [iconPosition]=\"\r\n              tab.iconPosition === 'end'\r\n                ? 'right'\r\n                : tab.iconPosition === 'start'\r\n                ? 'left'\r\n                : 'left'\r\n            \" [disabled]=\"!!tab.disabled\" [attr.aria-selected]=\"tab.value === value\"\r\n            [attr.aria-disabled]=\"tab.disabled\" [attr.tabindex]=\"tab.disabled ? -1 : 0\" [attr.aria-label]=\"tab.label\"\r\n            [attr.title]=\"tab.label\" (userClick)=\"onTabClick(tab, $event)\" (mouseenter)=\"\r\n              tab.dropdown ? onTabDropdownEnter(i) : null; onTabHover(tab)\r\n            \" (mouseleave)=\"\r\n              tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n            \" (focusin)=\"\r\n              tab.dropdown ? onTabDropdownEnter(i) : null; onTabFocus(tab)\r\n            \" (focusout)=\"\r\n              tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n            \" [variant]=\"buttonProps.variant || 'secondary'\" [size]=\"buttonProps.size || 'normal'\" [state]=\"\r\n              tab.value === value ? 'active' : buttonProps.state || 'default'\r\n            \" [visual]=\"buttonProps.visual || 'normal'\" [pill]=\"buttonProps.pill ?? false\"\r\n            [width]=\"buttonProps.width || ''\" [height]=\"buttonProps.height || ''\"\r\n            [gradient]=\"buttonProps.gradient || ''\" [background]=\"buttonProps.background || ''\"\r\n            [color]=\"buttonProps.color || ''\" [iconColor]=\"buttonProps.iconColor || ''\"\r\n            [iconSize]=\"buttonProps.iconSize ?? 18\" [dropdown]=\"!!tab.dropdown\">\r\n            <ng-container *ngIf=\"tab.dropdown\">\r\n              <span class=\"ava-tabs__dropdown-chevron\" aria-hidden=\"true\">▼</span>\r\n            </ng-container>\r\n          </ava-button>\r\n        </ng-container>\r\n        <ng-template #notButtonVariant>\r\n          <button *ngFor=\"let tab of tabs; let i = index\" #tabButton class=\"ava-tabs__tab\" [ngClass]=\"{\r\n              'icon-top': tab.iconPosition === 'top',\r\n              'icon-bottom': tab.iconPosition === 'bottom',\r\n              'ava-tabs__tab--has-dropdown': tab.dropdown\r\n            }\" [class.ava-tabs__tab--active]=\"tab.value === value\" [class.ava-tabs__tab--disabled]=\"tab.disabled\"\r\n            [attr.aria-selected]=\"tab.value === value\" [attr.aria-disabled]=\"tab.disabled\"\r\n            [attr.tabindex]=\"tab.disabled ? -1 : 0\" [attr.aria-label]=\"variant === 'icon' ? tab.label : null\"\r\n            [attr.title]=\"variant === 'icon' ? tab.label : null\" (click)=\"onTabClick(tab)\" (mouseenter)=\"\r\n              tab.dropdown ? onTabDropdownEnter(i, tabButton) : null;\r\n              onTabHover(tab)\r\n            \" (mouseleave)=\"\r\n              tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n            \" (focusin)=\"\r\n              tab.dropdown ? onTabDropdownEnter(i, tabButton) : null;\r\n              onTabFocus(tab)\r\n            \" (focusout)=\"\r\n              tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n            \" type=\"button\" role=\"tab\">\r\n            <ng-container *ngIf=\"variant === 'icon'; else normalTabContent\">\r\n              <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\" [iconColor]=\"'var(--tab-icon-color)'\"\r\n                [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n            </ng-container>\r\n            <ng-template #normalTabContent>\r\n              <ng-container [ngSwitch]=\"tab.iconPosition\">\r\n                <ng-container *ngSwitchCase=\"'top'\">\r\n                  <div class=\"ava-tabs__icon-top\">\r\n                    <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                      [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                  </div>\r\n                  <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                </ng-container>\r\n                <ng-container *ngSwitchCase=\"'bottom'\">\r\n                  <div class=\"ava-tabs__icon-bottom\">\r\n                    <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                      [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                  </div>\r\n                  <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                </ng-container>\r\n                <ng-container *ngSwitchCase=\"'start'\">\r\n                  <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\" [iconColor]=\"'var(--tab-icon-color)'\"\r\n                    [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                  <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                </ng-container>\r\n                <ng-container *ngSwitchCase=\"'end'\">\r\n                  <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                  <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\" [iconColor]=\"'var(--tab-icon-color)'\"\r\n                    [cursor]=\"false\" [disabled]=\"!!tab.disabled\" class=\"ava-tabs__icon\"></ava-icon>\r\n                </ng-container>\r\n                <ng-container *ngSwitchDefault>\r\n                  <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                </ng-container>\r\n              </ng-container>\r\n              <ng-container *ngIf=\"tab.dropdown\">\r\n                <span class=\"ava-tabs__dropdown-arrow\" aria-hidden=\"true\">▼</span>\r\n              </ng-container>\r\n            </ng-template>\r\n          </button>\r\n        </ng-template>\r\n        <div *ngIf=\"variant !== 'button' && variant !== 'icon'\" class=\"ava-tabs__underline\"\r\n          [style.width.px]=\"underline.width\" [style.transform]=\"'translateX(' + underline.left + 'px)'\"></div>\r\n      </nav>\r\n      <button *ngIf=\"showScrollButtons\" class=\"awe-tabs__scroll-btn awe-tabs__scroll-btn--left\" (click)=\"scroll('left')\"\r\n        [disabled]=\"disableScrollLeft\">\r\n        <ava-icon iconName=\"chevron-left\" [iconSize]=\"18\" [iconColor]=\"'grey'\" [cursor]=\"false\"></ava-icon>\r\n      </button>\r\n      <button *ngIf=\"showScrollButtons\" class=\"awe-tabs__scroll-btn awe-tabs__scroll-btn--right\"\r\n        (click)=\"scroll('right')\" [disabled]=\"disableScrollRight\">\r\n        <ava-icon iconName=\"chevron-right\" [iconSize]=\"18\" [iconColor]=\"'grey'\" [cursor]=\"false\"></ava-icon>\r\n      </button>\r\n    </div>\r\n  </div>\r\n  <div class=\"ava-tabs__content\" *ngIf=\"activeTab?.content\">\r\n    {{ activeTab?.content }}\r\n  </div>\r\n</ng-template>\r\n<div *ngIf=\"openDropdownIndex !== null\" #dropdownMenu class=\"ava-tabs__dropdown-menu ava-tabs__dropdown-menu--portal\"\r\n  [ngStyle]=\"dropdownMenuStyle\" [style.left.px]=\"dropdownPosition?.left\" [style.top.px]=\"dropdownPosition?.top\"\r\n  (mouseenter)=\"onDropdownMenuEnter(openDropdownIndex, dropdownMenu)\"\r\n  (mouseleave)=\"onDropdownMenuLeave(openDropdownIndex)\" (focusout)=\"onDropdownMenuLeave(openDropdownIndex)\">\r\n  <button *ngFor=\"let item of tabs[openDropdownIndex]?.dropdown?.items\" class=\"ava-tabs__dropdown-item\" type=\"button\"\r\n    (click)=\"onDropdownItemClick(tabs[openDropdownIndex], item)\"\r\n    (mouseenter)=\"onDropdownItemHover(tabs[openDropdownIndex], item)\"\r\n    (focusin)=\"onDropdownItemFocus(tabs[openDropdownIndex], item)\"\r\n    (focusout)=\"onDropdownItemBlur(tabs[openDropdownIndex], item)\">\r\n    <ava-icon *ngIf=\"item.icon\" [iconName]=\"item.icon\" [iconSize]=\"18\"\r\n      [iconColor]=\"item.iconColor || 'var(--tab-dropdown-item-color)'\" [cursor]=\"false\" [disabled]=\"false\"></ava-icon>\r\n    <span class=\"ava-tabs__dropdown-label-group\">\r\n      <span class=\"ava-tabs__dropdown-label\">{{ item.label }}</span>\r\n      <span *ngIf=\"item.subtitle\" class=\"ava-tabs__dropdown-subtitle\">{{\r\n        item.subtitle\r\n        }}</span>\r\n    </span>\r\n  </button>\r\n</div>", "import { CommonModule } from '@angular/common';\r\nimport {\r\n  ChangeDetectionStrategy,\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  Output,\r\n} from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'ava-pagination-controls',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './pagination-controls.component.html',\r\n  styleUrl: './pagination-controls.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n})\r\nexport class PaginationControlsComponent {\r\n  @Input() currentPage = 1;\r\n  @Input() totalPages = 10;\r\n  @Input() type:\r\n    | 'basic'\r\n    | 'extended'\r\n    | 'standard'\r\n    | 'pageinfo'\r\n    | 'simplepageinfo' = 'basic';\r\n\r\n  @Output() pageChange = new EventEmitter<number>();\r\n\r\n  goToPage(page: number | string): void {\r\n    if (typeof page === 'number' && page !== this.currentPage) {\r\n      this.pageChange.emit(page);\r\n    }\r\n  }\r\n\r\n  // Return extended or basic pages depending on type\r\n  get pages(): (number | string)[] {\r\n    switch (this.type) {\r\n      case 'basic':\r\n        return this.getBasicPages();\r\n      case 'extended':\r\n        return this.getExtendedPages();\r\n      case 'standard':\r\n        return this.getStandardPages();\r\n      default:\r\n        return [];\r\n    }\r\n  }\r\n\r\n  private getBasicPages(): (number | string)[] {\r\n    const pages: (number | string)[] = [];\r\n    const { currentPage, totalPages } = this;\r\n\r\n    if (totalPages <= 7) {\r\n      for (let i = 1; i <= totalPages; i++) pages.push(i);\r\n    } else {\r\n      pages.push(1);\r\n      if (currentPage > 3) pages.push('...');\r\n      const start = Math.max(2, currentPage - 1);\r\n      const end = Math.min(totalPages - 1, currentPage + 1);\r\n      for (let i = start; i <= end; i++) pages.push(i);\r\n      if (currentPage < totalPages - 2) pages.push('...');\r\n      pages.push(totalPages);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  private getExtendedPages(): number[] {\r\n    return Array.from({ length: this.totalPages }, (_, i) => i + 1);\r\n  }\r\n\r\n  private getStandardPages(): (number | string)[] {\r\n    const { currentPage, totalPages } = this;\r\n    const pages: (number | string)[] = [];\r\n\r\n    const firstPages = [1, 2, 3];\r\n    const lastPages = [totalPages - 2, totalPages - 1, totalPages];\r\n\r\n    // Add first 3 always\r\n    firstPages.forEach((p) => {\r\n      if (p <= totalPages) pages.push(p);\r\n    });\r\n\r\n    // Case: currentPage <= 3 → show only beginning\r\n    if (currentPage <= 3) {\r\n      pages.push('...');\r\n      lastPages.forEach((p) => {\r\n        if (!pages.includes(p) && p > 3) pages.push(p);\r\n      });\r\n      return pages;\r\n    }\r\n\r\n    // Case: currentPage == 4 → slight expansion\r\n    if (currentPage === 4) {\r\n      pages.push(4);\r\n      pages.push('...');\r\n      lastPages.forEach((p) => {\r\n        if (!pages.includes(p)) pages.push(p);\r\n      });\r\n      return pages;\r\n    }\r\n\r\n    // Case: currentPage in middle\r\n    if (currentPage > 4 && currentPage < totalPages - 3) {\r\n      pages.push('...');\r\n      pages.push(currentPage);\r\n      pages.push('...');\r\n      lastPages.forEach((p) => {\r\n        if (!pages.includes(p)) pages.push(p);\r\n      });\r\n      return pages;\r\n    }\r\n\r\n    // Case: currentPage is near the end (≥ totalPages - 3)\r\n    if (currentPage >= totalPages - 3) {\r\n      pages.push('...');\r\n      for (let i = totalPages - 3; i <= totalPages; i++) {\r\n        if (!pages.includes(i) && i > 3) pages.push(i);\r\n      }\r\n      return pages;\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  shouldShow(page: number | string): boolean {\r\n    if (typeof page !== 'number') {\r\n      return false;\r\n    }\r\n\r\n    const { currentPage, totalPages } = this;\r\n    const firstPages = [1, 2, 3];\r\n    const lastPages = [totalPages - 1, totalPages];\r\n\r\n    return (\r\n      firstPages.includes(page) ||\r\n      lastPages.includes(page) ||\r\n      Math.abs(currentPage - page) <= 1\r\n    );\r\n  }\r\n\r\n  shouldInsertDots(index: number): boolean {\r\n    const pages = this.pages;\r\n    const curr = pages[index];\r\n    const next = pages[index + 1];\r\n\r\n    return (\r\n      typeof curr === 'number' &&\r\n      typeof next === 'number' &&\r\n      this.shouldShow(next) &&\r\n      !this.shouldShow(curr)\r\n    );\r\n  }\r\n\r\n  nextPage(): void {\r\n    if (this.currentPage < this.totalPages) {\r\n      this.pageChange.emit(this.currentPage + 1);\r\n    }\r\n  }\r\n\r\n  prevPage(): void {\r\n    if (this.currentPage > 1) {\r\n      this.pageChange.emit(this.currentPage - 1);\r\n    }\r\n  }\r\n}\r\n", "<!-- TYPE: basic -->\r\n<ng-container *ngIf=\"type === 'basic'\">\r\n  <div class=\"pagination-container\">\r\n    <button (click)=\"prevPage()\" [disabled]=\"currentPage === 1\" class=\"nav-btn\">\r\n      ← Previous\r\n    </button>\r\n\r\n    <ng-container *ngFor=\"let page of pages\">\r\n      <button\r\n        *ngIf=\"page !== '...'; else dots\"\r\n        (click)=\"goToPage(page)\"\r\n        [ngClass]=\"{ active: page === currentPage }\"\r\n        class=\"page-btn\"\r\n      >\r\n        {{ page }}\r\n      </button>\r\n      <ng-template #dots>\r\n        <span class=\"dots\">...</span>\r\n      </ng-template>\r\n    </ng-container>\r\n\r\n    <button\r\n      (click)=\"nextPage()\"\r\n      [disabled]=\"currentPage === totalPages\"\r\n      class=\"nav-btn\"\r\n    >\r\n      Next →\r\n    </button>\r\n  </div>\r\n</ng-container>\r\n\r\n<!-- TYPE: extended -->\r\n<ng-container *ngIf=\"type === 'extended'\">\r\n  <div class=\"pagination-container\">\r\n    <button (click)=\"prevPage()\" [disabled]=\"currentPage === 1\" class=\"nav-btn\">\r\n      ← Previous\r\n    </button>\r\n\r\n    <ng-container *ngFor=\"let page of pages; let i = index\">\r\n      <ng-container *ngIf=\"shouldShow(page); else maybeDots\">\r\n        <button\r\n          (click)=\"goToPage(page)\"\r\n          [ngClass]=\"{ active: page === currentPage }\"\r\n          class=\"page-btn\"\r\n        >\r\n          {{ page }}\r\n        </button>\r\n      </ng-container>\r\n      <ng-template #maybeDots>\r\n        <ng-container *ngIf=\"shouldInsertDots(i)\">\r\n          <span class=\"dots\">...</span>\r\n        </ng-container>\r\n      </ng-template>\r\n    </ng-container>\r\n\r\n    <button\r\n      (click)=\"nextPage()\"\r\n      [disabled]=\"currentPage === totalPages\"\r\n      class=\"nav-btn\"\r\n    >\r\n      Next →\r\n    </button>\r\n  </div>\r\n</ng-container>\r\n\r\n<!-- TYPE: standard -->\r\n<ng-container *ngIf=\"type === 'standard'\">\r\n  <div class=\"pagination-container standard\">\r\n    <!-- Left: Previous -->\r\n    <div class=\"nav-left\">\r\n      <button\r\n        (click)=\"prevPage()\"\r\n        [disabled]=\"currentPage === 1\"\r\n        class=\"nav-btn\"\r\n      >\r\n        ← Previous\r\n      </button>\r\n    </div>\r\n    <!-- Center: Page numbers -->\r\n    <div class=\"pages\">\r\n      <ng-container *ngFor=\"let page of pages\">\r\n        <button\r\n          *ngIf=\"page !== '...'; else dots\"\r\n          (click)=\"goToPage(page)\"\r\n          [ngClass]=\"{ active: page === currentPage }\"\r\n          class=\"page-btn\"\r\n        >\r\n          {{ page }}\r\n        </button>\r\n        <ng-template #dots>\r\n          <span class=\"dots\">...</span>\r\n        </ng-template>\r\n      </ng-container>\r\n    </div>\r\n\r\n    <!-- Right: Next -->\r\n    <div class=\"nav-right\">\r\n      <button\r\n        (click)=\"nextPage()\"\r\n        [disabled]=\"currentPage === totalPages\"\r\n        class=\"nav-btn\"\r\n      >\r\n        Next →\r\n      </button>\r\n    </div>\r\n  </div>\r\n</ng-container>\r\n\r\n<!-- TYPE: pageinfo -->\r\n<ng-container *ngIf=\"type === 'pageinfo'\">\r\n  <div class=\"pagination-container page\">\r\n    <button class=\"nav-btn\" (click)=\"prevPage()\" [disabled]=\"currentPage === 1\">\r\n      ← Previous\r\n    </button>\r\n\r\n    <div class=\"page-label\">Page {{ currentPage }} of {{ totalPages }}</div>\r\n\r\n    <button\r\n      class=\"nav-btn\"\r\n      (click)=\"nextPage()\"\r\n      [disabled]=\"currentPage === totalPages\"\r\n    >\r\n      Next →\r\n    </button>\r\n  </div>\r\n</ng-container>\r\n\r\n<!-- TYPE: simplepageinfo -->\r\n<ng-container *ngIf=\"type === 'simplepageinfo'\">\r\n  <div class=\"pagination-container simplepage\">\r\n    <button\r\n      class=\"icon-btn\"\r\n      (click)=\"prevPage()\"\r\n      [disabled]=\"currentPage === 1\"\r\n      aria-label=\"Previous page\"\r\n    >\r\n      ←\r\n    </button>\r\n\r\n    <div class=\"page-label\">Page {{ currentPage }} of {{ totalPages }}</div>\r\n\r\n    <button\r\n      class=\"icon-btn\"\r\n      (click)=\"nextPage()\"\r\n      [disabled]=\"currentPage === totalPages\"\r\n      aria-label=\"Next page\"\r\n    >\r\n      →\r\n    </button>\r\n  </div>\r\n</ng-container>\r\n", "import { CommonModule } from '@angular/common';\r\nimport { ChangeDetectionStrategy, Component, Input } from '@angular/core';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\n\r\n@Component({\r\n  selector: 'ava-accordion',\r\n  imports: [CommonModule,LucideAngularModule],\r\n  standalone: true,\r\n  templateUrl: './accordion.component.html',\r\n  styleUrl: './accordion.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n})\r\nexport class AccordionComponent {\r\n  @Input() expanded = false;\r\n  @Input() animation = false;\r\n  @Input() controlled = false;\r\n  @Input() iconClosed = ''; \r\n  @Input() iconOpen = '';  \r\n  @Input() titleIcon = '';  \r\n  @Input() iconPosition: 'left' | 'right' = 'left';\r\n  @Input() type: 'default' | 'titleIcon' = 'default';\r\n  get accordionClasses() {\r\n    return {\r\n      animated: this.animation,\r\n      expanded: this.expanded,\r\n    };\r\n  }\r\n\r\n  onAccordionKeydown(event: KeyboardEvent) {\r\n    if (event.key === 'Enter' || event.key === ' ') {\r\n      this.toggleExpand();\r\n      event.preventDefault();\r\n    }\r\n  }\r\n\r\n  toggleExpand() {\r\n    if (!this.controlled) {\r\n      this.expanded = !this.expanded;\r\n    }\r\n  }\r\n}\r\n", "<div class=\"accordion-container\" [ngClass]=\"accordionClasses\">\r\n  <div\r\n    class=\"accordion-header\"\r\n    (click)=\"toggleExpand()\"\r\n    tabindex=\"0\"\r\n    role=\"button\"\r\n    [attr.aria-expanded]=\"expanded\"\r\n    (keydown.enter)=\"toggleExpand()\"\r\n    (keydown.space)=\"toggleExpand()\"\r\n  >\r\n    <div class=\"header-row\">\r\n      <!-- TYPE: titleIcon -->\r\n      <ng-container *ngIf=\"type === 'titleIcon'\">\r\n        <!-- LEFT: Static Title Icon -->\r\n        <span class=\"icon\">\r\n          <lucide-icon\r\n            [name]=\"titleIcon\"\r\n            class=\"accordion-title-icon\"\r\n            [attr.aria-hidden]=\"true\"\r\n          ></lucide-icon>\r\n        </span>\r\n      </ng-container>\r\n\r\n      <!-- DEFAULT ICON LEFT -->\r\n      <ng-container *ngIf=\"type !== 'titleIcon' && iconPosition === 'left'\">\r\n        <span class=\"icon\">\r\n          <lucide-icon\r\n            [name]=\"expanded ? iconOpen : iconClosed\"\r\n            class=\"accordion-icon\"\r\n            [attr.aria-hidden]=\"true\"\r\n          ></lucide-icon>\r\n        </span>\r\n      </ng-container>\r\n\r\n      <!-- TITLE: Always Rendered -->\r\n      <span class=\"accordion-title\">\r\n        <span class=\"accordion-title-highlight\">\r\n          <ng-content select=\"[header]\"></ng-content>\r\n        </span>\r\n      </span>\r\n\r\n      <!-- ICON RIGHT -->\r\n      <ng-container\r\n        *ngIf=\"\r\n          (type !== 'titleIcon' && iconPosition === 'right') ||\r\n          type === 'titleIcon'\r\n        \"\r\n      >\r\n        <span class=\"icon right-aligned-icon\">\r\n          <lucide-icon\r\n            [name]=\"expanded ? iconOpen : iconClosed\"\r\n            class=\"accordion-icon\"\r\n            [attr.aria-hidden]=\"true\"\r\n          ></lucide-icon>\r\n        </span>\r\n      </ng-container>\r\n    </div>\r\n  </div>\r\n\r\n  <div *ngIf=\"expanded\">\r\n    <div\r\n      class=\"accordion-body\"\r\n      [ngClass]=\"{ 'animated-content': animation, show: expanded }\"\r\n    >\r\n      <ng-content select=\"[content]\"></ng-content>\r\n    </div>\r\n  </div>\r\n</div>\r\n", "import {\r\n  ChangeDetectionStrategy,\r\n  Component,\r\n  Input,\r\n  forwardRef,\r\n  Output,\r\n  EventEmitter,\r\n  ChangeDetectorRef,\r\n  CUSTOM_ELEMENTS_SCHEMA,\r\n  ViewChild,\r\n  ElementRef,\r\n  AfterViewInit\r\n} from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IconComponent } from '../icon/icon.component';\r\nimport { trigger, transition, style, animate } from '@angular/animations';\r\n\r\nexport type TextboxVariant = 'default' | 'primary' | 'success' | 'error' | 'warning' | 'info';\r\nexport type TextboxSize = 'sm' | 'md' | 'lg';\r\nexport type IconPosition = 'start' | 'end';\r\n\r\n// Play+ Metaphor System Types\r\nexport type PersonalityTheme = 'minimal' | 'professional' | 'modern' | 'vibrant';\r\nexport type MetaphorIntensity = 0 | 10 | 25 | 50 | 75 | 100;\r\n\r\n@Component({\r\n  selector: 'ava-textbox',\r\n  standalone: true,\r\n  imports: [CommonModule, IconComponent],\r\n  templateUrl: './ava-textbox.component.html',\r\n  styleUrl: './ava-textbox.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => AvaTextboxComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  animations: [\r\n    trigger('fadeIcon', [\r\n      transition(':enter', [\r\n        style({ opacity: 0, transform: 'scale(0.8)' }),\r\n        animate('180ms cubic-bezier(0.4,0,0.2,1)', style({ opacity: 1, transform: 'scale(1)' }))\r\n      ]),\r\n      transition(':leave', [\r\n        animate('180ms cubic-bezier(0.4,0,0.2,1)', style({ opacity: 0, transform: 'scale(0.8)' }))\r\n      ])\r\n    ])\r\n  ]\r\n})\r\nexport class AvaTextboxComponent implements ControlValueAccessor, AfterViewInit {\r\n  @Input() label = '';\r\n  @Input() placeholder = '';\r\n  @Input() variant: TextboxVariant = 'default';\r\n  @Input() size: TextboxSize = 'md';\r\n  @Input() disabled = false;\r\n  @Input() readonly = false;\r\n  @Input() error = '';\r\n  @Input() helper = '';\r\n  @Input() icon = '';\r\n  @Input() iconPosition: IconPosition = 'start';\r\n  @Input() iconColor = 'var(--textbox-icon-color)';\r\n  @Input() id = '';\r\n  @Input() name = '';\r\n  @Input() autocomplete = '';\r\n  @Input() type = 'text';\r\n  @Input() maxlength?: number;\r\n  @Input() minlength?: number;\r\n  @Input() required = false;\r\n  @Input() fullWidth = false;\r\n  @Input() style?: Record<string, string>;\r\n  \r\n  // Play+ Metaphor System Props\r\n  @Input() personality?: PersonalityTheme; // Override global personality\r\n  @Input() glassIntensity?: MetaphorIntensity; // Override personality glass\r\n  @Input() lightIntensity?: MetaphorIntensity; // Override personality light\r\n  @Input() liquidIntensity?: MetaphorIntensity; // Override personality liquid\r\n  @Input() gradientIntensity?: MetaphorIntensity; // Override personality gradient\r\n  @Input() enableMetaphors = true; // Master toggle for metaphor system\r\n  @Input() respectsGlobalPersonality = true; // Whether to inherit from global personality\r\n  \r\n  // Legacy metaphor support\r\n  @Input() metaphor: string | string[] = '';\r\n\r\n  @Output() textboxBlur = new EventEmitter<Event>();\r\n  @Output() textboxFocus = new EventEmitter<Event>();\r\n  @Output() textboxInput = new EventEmitter<Event>();\r\n  @Output() textboxChange = new EventEmitter<Event>();\r\n  @Output() iconStartClick = new EventEmitter<Event>();\r\n  @Output() iconEndClick = new EventEmitter<Event>();\r\n\r\n  @ViewChild('prefixContainer', { static: true }) prefixContainer!: ElementRef;\r\n  @ViewChild('suffixContainer', { static: true }) suffixContainer!: ElementRef;\r\n  @ViewChild('iconStartContainer', { static: true }) iconStartContainer!: ElementRef;\r\n  @ViewChild('iconEndContainer', { static: true }) iconEndContainer!: ElementRef;\r\n\r\n  value = '';\r\n  isFocused = false;\r\n  hasProjectedPrefix = false;\r\n  hasProjectedSuffix = false;\r\n  hasProjectedStartIcon = false;\r\n  hasProjectedEndIcon = false;\r\n  \r\n  private onChange: (value: string) => void = () => { /* noop */ };\r\n  private onTouched: () => void = () => { /* noop */ };\r\n\r\n  constructor(private cdr: ChangeDetectorRef) {}\r\n\r\n  ngAfterViewInit(): void {\r\n    // Check for projected content in containers\r\n    this.checkProjectedContent();\r\n  }\r\n\r\n  private checkProjectedContent(): void {\r\n    // Use setTimeout to ensure content projection has completed\r\n    setTimeout(() => {\r\n      this.hasProjectedPrefix = this.prefixContainer?.nativeElement?.children?.length > 0;\r\n      this.hasProjectedSuffix = this.suffixContainer?.nativeElement?.children?.length > 0;\r\n      this.hasProjectedStartIcon = this.iconStartContainer?.nativeElement?.children?.length > 0;\r\n      this.hasProjectedEndIcon = this.iconEndContainer?.nativeElement?.children?.length > 0;\r\n      this.cdr.markForCheck();\r\n    });\r\n  }\r\n\r\n  // ControlValueAccessor implementation\r\n  writeValue(value: string): void {\r\n    this.value = value || '';\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  registerOnChange(fn: (value: string) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  // Event handlers\r\n  onInput(event: Event): void {\r\n    const target = event.target as HTMLInputElement;\r\n    this.value = target.value;\r\n    this.onChange(this.value);\r\n    this.textboxInput.emit(event);\r\n  }\r\n\r\n  onFocus(event: Event): void {\r\n    this.isFocused = true;\r\n    this.textboxFocus.emit(event);\r\n  }\r\n\r\n  onBlur(event: Event): void {\r\n    this.isFocused = false;\r\n    this.onTouched();\r\n    this.textboxBlur.emit(event);\r\n  }\r\n\r\n  onChange_(event: Event): void {\r\n    this.textboxChange.emit(event);\r\n  }\r\n\r\n  // Icon click handlers\r\n  onIconStartClick(event: Event): void {\r\n    if (this.disabled || this.readonly) return;\r\n    event.stopPropagation();\r\n    this.iconStartClick.emit(event);\r\n  }\r\n\r\n  onIconEndClick(event: Event): void {\r\n    if (this.disabled || this.readonly) return;\r\n    event.stopPropagation();\r\n    this.iconEndClick.emit(event);\r\n  }\r\n\r\n  // Keyboard accessibility for icons\r\n  onIconKeydown(event: KeyboardEvent, position: 'start' | 'end'): void {\r\n    if (event.key === 'Enter' || event.key === ' ') {\r\n      event.preventDefault();\r\n      if (position === 'start') this.onIconStartClick(event);\r\n      else this.onIconEndClick(event);\r\n    }\r\n  }\r\n\r\n  // Computed properties\r\n  get hasError(): boolean {\r\n    return !!this.error;\r\n  }\r\n\r\n  get hasHelper(): boolean {\r\n    return !!this.helper && !this.hasError;\r\n  }\r\n\r\n  get hasIcon(): boolean {\r\n    return !!this.icon;\r\n  }\r\n\r\n\r\n\r\n  get inputId(): string {\r\n    return this.id || `ava-textbox-${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  get errorId(): string {\r\n    return `${this.inputId}-error`;\r\n  }\r\n\r\n  get helperId(): string {\r\n    return `${this.inputId}-helper`;\r\n  }\r\n\r\n  get ariaDescribedBy(): string {\r\n    const ids: string[] = [];\r\n    if (this.hasError) ids.push(this.errorId);\r\n    if (this.hasHelper) ids.push(this.helperId);\r\n    return ids.join(' ') || '';\r\n  }\r\n\r\n  /**\r\n   * Generate metaphor intensity classes based on props and personality\r\n   */\r\n  get metaphorClasses(): string[] {\r\n    if (!this.enableMetaphors) return [];\r\n\r\n    const classes: string[] = [];\r\n\r\n    // Add personality class if provided\r\n    if (this.personality) {\r\n      classes.push(`ava-textbox--personality-${this.personality}`);\r\n    }\r\n\r\n    // Add specific intensity overrides\r\n    if (this.glassIntensity !== undefined) {\r\n      classes.push(`ava-textbox--glass-${this.glassIntensity}`);\r\n    }\r\n    if (this.lightIntensity !== undefined) {\r\n      classes.push(`ava-textbox--light-${this.lightIntensity}`);\r\n    }\r\n    if (this.liquidIntensity !== undefined) {\r\n      classes.push(`ava-textbox--liquid-${this.liquidIntensity}`);\r\n    }\r\n    if (this.gradientIntensity !== undefined) {\r\n      classes.push(`ava-textbox--gradient-${this.gradientIntensity}`);\r\n    }\r\n\r\n    return classes;\r\n  }\r\n\r\n  /**\r\n   * Generate legacy metaphor classes for backward compatibility\r\n   */\r\n  get legacyMetaphorClasses(): string[] {\r\n    if (!this.metaphor) return [];\r\n    \r\n    if (Array.isArray(this.metaphor)) {\r\n      return this.metaphor.map(m => `ava-textbox--${m}`);\r\n    } else {\r\n      return [`ava-textbox--${this.metaphor}`];\r\n    }\r\n  }\r\n\r\n  get inputClasses(): string {\r\n    const classes = ['ava-textbox__input'];\r\n    \r\n    if (this.size) classes.push(`ava-textbox__input--${this.size}`);\r\n    if (this.variant) classes.push(`ava-textbox__input--${this.variant}`);\r\n    if (this.hasError) classes.push('ava-textbox__input--error');\r\n    if (this.disabled) classes.push('ava-textbox__input--disabled');\r\n    if (this.readonly) classes.push('ava-textbox__input--readonly');\r\n    if (this.isFocused) classes.push('ava-textbox__input--focused');\r\n    if (this.fullWidth) classes.push('ava-textbox__input--full-width');\r\n    \r\n    // Add classes based on projected content\r\n    if (this.hasProjectedStartIcon) classes.push('ava-textbox__input--icon-start');\r\n    if (this.hasProjectedEndIcon) classes.push('ava-textbox__input--icon-end');\r\n    if (this.hasProjectedPrefix) classes.push('ava-textbox__input--with-prefix');\r\n    if (this.hasProjectedSuffix) classes.push('ava-textbox__input--with-suffix');\r\n    \r\n    return classes.join(' ');\r\n  }\r\n\r\n  get wrapperClasses(): string {\r\n    const classes = ['ava-textbox'];\r\n    \r\n    if (this.size) classes.push(`ava-textbox--${this.size}`);\r\n    if (this.variant) classes.push(`ava-textbox--${this.variant}`);\r\n    if (this.hasError) classes.push('ava-textbox--error');\r\n    if (this.disabled) classes.push('ava-textbox--disabled');\r\n    if (this.readonly) classes.push('ava-textbox--readonly');\r\n    if (this.isFocused) classes.push('ava-textbox--focused');\r\n    if (this.fullWidth) classes.push('ava-textbox--full-width');\r\n    \r\n    // Add Play+ metaphor classes\r\n    const metaphorClasses = this.metaphorClasses;\r\n    const legacyClasses = this.legacyMetaphorClasses;\r\n    \r\n    return [...classes, ...metaphorClasses, ...legacyClasses].join(' ');\r\n  }\r\n}\r\n", "<div [class]=\"wrapperClasses\">\r\n  <!-- Label -->\r\n  <label\r\n    *ngIf=\"label\"\r\n    [for]=\"inputId\"\r\n    class=\"ava-textbox__label\"\r\n    [class.ava-textbox__label--required]=\"required\"\r\n  >\r\n    {{ label }}\r\n    <span *ngIf=\"required\" class=\"ava-textbox__required\" aria-hidden=\"true\"\r\n      >*</span\r\n    >\r\n  </label>\r\n\r\n  <!-- Input Container -->\r\n  <div class=\"ava-textbox__container\" [ngStyle]=\"style\">\r\n    <!-- Prefix Slot -->\r\n    <div class=\"ava-textbox__prefix\" #prefixContainer>\r\n      <ng-content select=\"[slot=prefix]\"></ng-content>\r\n    </div>\r\n\r\n    <!-- Start Projected Icons (before input/textarea) -->\r\n    <div\r\n      class=\"ava-textbox__icons ava-textbox__icons--start\"\r\n      #iconStartContainer\r\n    >\r\n      <ng-content select=\"[slot=icon-start]\"></ng-content>\r\n    </div>\r\n\r\n    <!-- Input Field -->\r\n    <input\r\n      [id]=\"inputId\"\r\n      [name]=\"name\"\r\n      [type]=\"type\"\r\n      [placeholder]=\"placeholder\"\r\n      [value]=\"value\"\r\n      [disabled]=\"disabled\"\r\n      [readonly]=\"readonly\"\r\n      [required]=\"required\"\r\n      [attr.maxlength]=\"maxlength\"\r\n      [attr.minlength]=\"minlength\"\r\n      [autocomplete]=\"autocomplete\"\r\n      [class]=\"inputClasses\"\r\n      [attr.aria-invalid]=\"hasError\"\r\n      [attr.aria-describedby]=\"ariaDescribedBy || null\"\r\n      (input)=\"onInput($event)\"\r\n      (focus)=\"onFocus($event)\"\r\n      (blur)=\"onBlur($event)\"\r\n      (change)=\"onChange_($event)\"\r\n    />\r\n\r\n    <!-- End Projected Icons (before input/textarea) -->\r\n    <div class=\"ava-textbox__icons ava-textbox__icons--end\" #iconEndContainer>\r\n      <ng-content select=\"[slot=icon-end]\"></ng-content>\r\n    </div>\r\n\r\n    <!-- Suffix Slot -->\r\n    <div class=\"ava-textbox__suffix\" #suffixContainer>\r\n      <ng-content select=\"[slot=suffix]\"></ng-content>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Error Message -->\r\n  <div\r\n    *ngIf=\"hasError\"\r\n    [id]=\"errorId\"\r\n    class=\"ava-textbox__error\"\r\n    role=\"alert\"\r\n    aria-live=\"polite\"\r\n  >\r\n    <ava-icon\r\n      iconName=\"alert-circle\"\r\n      [iconSize]=\"14\"\r\n      class=\"ava-textbox__error-icon\"\r\n      [cursor]=\"false\"\r\n      [disabled]=\"false\"\r\n      [iconColor]=\"'red'\"\r\n    ></ava-icon>\r\n    <span class=\"ava-textbox__error-text\">{{ error }}</span>\r\n  </div>\r\n\r\n  <!-- Helper Message -->\r\n  <div *ngIf=\"hasHelper\" [id]=\"helperId\" class=\"ava-textbox__helper\">\r\n    <ava-icon\r\n      iconName=\"info\"\r\n      [iconSize]=\"14\"\r\n      class=\"ava-textbox__helper-icon\"\r\n      [cursor]=\"false\"\r\n      [disabled]=\"false\"\r\n    ></ava-icon>\r\n    <span class=\"ava-textbox__helper-text\">{{ helper }}</span>\r\n  </div>\r\n</div>\r\n", "import {\r\n  ChangeDetectionStrategy,\r\n  Component,\r\n  Input,\r\n  forwardRef,\r\n  Output,\r\n  EventEmitter,\r\n  ChangeDetectorRef,\r\n  CUSTOM_ELEMENTS_SCHEMA\r\n} from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IconComponent } from '../icon/icon.component';\r\n\r\nexport type TextareaVariant = 'default' | 'primary' | 'success' | 'error' | 'warning' | 'info';\r\nexport type TextareaSize = 'sm' | 'md' | 'lg';\r\n\r\n@Component({\r\n  selector: 'ava-textarea',\r\n  standalone: true,\r\n  imports: [CommonModule, IconComponent],\r\n  templateUrl: './ava-textarea.component.html',\r\n  styleUrl: './ava-textarea.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => AvaTextareaComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n})\r\nexport class AvaTextareaComponent implements ControlValueAccessor {\r\n  @Input() label = '';\r\n  @Input() placeholder = '';\r\n  @Input() variant: TextareaVariant = 'default';\r\n  @Input() size: TextareaSize = 'md';\r\n  @Input() disabled = false;\r\n  @Input() readonly = false;\r\n  @Input() error = '';\r\n  @Input() helper = '';\r\n  @Input() rows = 3;\r\n  @Input() id = '';\r\n  @Input() name = '';\r\n  @Input() maxlength?: number;\r\n  @Input() minlength?: number;\r\n  @Input() required = false;\r\n  @Input() fullWidth = false;\r\n  @Input() style?: Record<string, string>;\r\n  @Input() resizable = true;\r\n\r\n  @Output() textareaBlur = new EventEmitter<Event>();\r\n  @Output() textareaFocus = new EventEmitter<Event>();\r\n  @Output() textareaInput = new EventEmitter<Event>();\r\n  @Output() textareaChange = new EventEmitter<Event>();\r\n  @Output() iconStartClick = new EventEmitter<Event>();\r\n  @Output() iconEndClick = new EventEmitter<Event>();\r\n\r\n  value = '';\r\n  isFocused = false;\r\n\r\n  constructor(private cdr: ChangeDetectorRef) {}\r\n\r\n  // ControlValueAccessor implementation\r\n  writeValue(value: string): void {\r\n    this.value = value || '';\r\n    this.cdr.markForCheck();\r\n  }\r\n  // These are set by Angular forms\r\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\r\n  onChange: (value: string) => void = () => {};\r\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\r\n  onTouched: () => void = () => {};\r\n  registerOnChange(fn: (value: string) => void): void { this.onChange = fn; }\r\n  registerOnTouched(fn: () => void): void { this.onTouched = fn; }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  // Event handlers\r\n  onInput(event: Event): void {\r\n    const target = event.target as HTMLTextAreaElement;\r\n    this.value = target.value;\r\n    this.onChange(this.value);\r\n    this.textareaInput.emit(event);\r\n  }\r\n  onFocus(event: Event): void {\r\n    this.isFocused = true;\r\n    this.textareaFocus.emit(event);\r\n  }\r\n  onBlur(event: Event): void {\r\n    this.isFocused = false;\r\n    this.onTouched();\r\n    this.textareaBlur.emit(event);\r\n  }\r\n  onChange_(event: Event): void {\r\n    this.textareaChange.emit(event);\r\n  }\r\n\r\n  // Icon click handlers\r\n  onIconStartClick(event: Event): void {\r\n    if (this.disabled || this.readonly) return;\r\n    event.stopPropagation();\r\n    this.iconStartClick.emit(event);\r\n  }\r\n  onIconEndClick(event: Event): void {\r\n    if (this.disabled || this.readonly) return;\r\n    event.stopPropagation();\r\n    this.iconEndClick.emit(event);\r\n  }\r\n  onIconKeydown(event: KeyboardEvent, position: 'start' | 'end'): void {\r\n    if (event.key === 'Enter' || event.key === ' ') {\r\n      event.preventDefault();\r\n      if (position === 'start') this.onIconStartClick(event);\r\n      else this.onIconEndClick(event);\r\n    }\r\n  }\r\n\r\n  // Computed properties\r\n  get hasError(): boolean {\r\n    return !!this.error;\r\n  }\r\n  get hasHelper(): boolean {\r\n    return !!this.helper;\r\n  }\r\n  get inputId(): string {\r\n    return this.id || `ava-textarea-${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n  get errorId(): string {\r\n    return `${this.inputId}-error`;\r\n  }\r\n  get helperId(): string {\r\n    return `${this.inputId}-helper`;\r\n  }\r\n  get ariaDescribedBy(): string {\r\n    const ids: string[] = [];\r\n    if (this.hasError) ids.push(this.errorId);\r\n    if (this.hasHelper) ids.push(this.helperId);\r\n    return ids.join(' ') || '';\r\n  }\r\n  get inputClasses(): string {\r\n    const classes = ['ava-textarea__input'];\r\n    if (this.size) classes.push(`ava-textarea__input--${this.size}`);\r\n    if (this.variant) classes.push(`ava-textarea__input--${this.variant}`);\r\n    if (this.hasError) classes.push('ava-textarea__input--error');\r\n    if (this.disabled) classes.push('ava-textarea__input--disabled');\r\n    if (this.readonly) classes.push('ava-textarea__input--readonly');\r\n    if (this.isFocused) classes.push('ava-textarea__input--focused');\r\n    if (this.fullWidth) classes.push('ava-textarea__input--full-width');\r\n    return classes.join(' ');\r\n  }\r\n  get wrapperClasses(): string {\r\n    const classes = ['ava-textarea'];\r\n    if (this.size) classes.push(`ava-textarea--${this.size}`);\r\n    if (this.variant) classes.push(`ava-textarea--${this.variant}`);\r\n    if (this.hasError) classes.push('ava-textarea--error');\r\n    if (this.disabled) classes.push('ava-textarea--disabled');\r\n    if (this.readonly) classes.push('ava-textarea--readonly');\r\n    if (this.isFocused) classes.push('ava-textarea--focused');\r\n    if (this.fullWidth) classes.push('ava-textarea--full-width');\r\n    return classes.join(' ');\r\n  }\r\n} ", "<div [class]=\"wrapperClasses\">\r\n  <!-- Label -->\r\n  <label\r\n    *ngIf=\"label\"\r\n    [for]=\"inputId\"\r\n    class=\"ava-textarea__label\"\r\n    [class.ava-textarea__label--required]=\"required\"\r\n  >\r\n    {{ label }}\r\n    <span *ngIf=\"required\" class=\"ava-textarea__required\" aria-hidden=\"true\"\r\n      >*</span\r\n    >\r\n  </label>\r\n\r\n  <!-- Input Container -->\r\n  <div class=\"ava-textarea__container\" [ngStyle]=\"style\">\r\n    <!-- Textarea Field -->\r\n    <textarea\r\n      [id]=\"inputId\"\r\n      [name]=\"name\"\r\n      [placeholder]=\"placeholder\"\r\n      [value]=\"value\"\r\n      [disabled]=\"disabled\"\r\n      [readonly]=\"readonly\"\r\n      [required]=\"required\"\r\n      [attr.maxlength]=\"maxlength\"\r\n      [attr.minlength]=\"minlength\"\r\n      [rows]=\"rows\"\r\n      [class]=\"inputClasses\"\r\n      [attr.aria-invalid]=\"hasError\"\r\n      [attr.aria-describedby]=\"ariaDescribedBy || null\"\r\n      [style.resize]=\"resizable ? 'vertical' : 'none'\"\r\n      (input)=\"onInput($event)\"\r\n      (focus)=\"onFocus($event)\"\r\n      (blur)=\"onBlur($event)\"\r\n      (change)=\"onChange_($event)\"\r\n    ></textarea>\r\n    <!-- Icons Bar (stacked below textarea) -->\r\n    <div class=\"ava-textarea__iconsbar\">\r\n      <div class=\"ava-textarea__icons ava-textarea__icons--start\">\r\n        <ng-content select=\"[slot=icon-start]\"></ng-content>\r\n      </div>\r\n      <div class=\"ava-textarea__iconsbar-spacer\"></div>\r\n      <div class=\"ava-textarea__icons ava-textarea__icons--end\">\r\n        <ng-content select=\"[slot=icon-end]\"></ng-content>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Error Message -->\r\n  <div\r\n    *ngIf=\"hasError\"\r\n    [id]=\"errorId\"\r\n    class=\"ava-textarea__error\"\r\n    role=\"alert\"\r\n    aria-live=\"polite\"\r\n  >\r\n    <ava-icon\r\n      iconName=\"alert-circle\"\r\n      [iconSize]=\"14\"\r\n      class=\"ava-textarea__error-icon\"\r\n      [cursor]=\"false\"\r\n      [disabled]=\"false\"\r\n      [iconColor]=\"'red'\"\r\n    ></ava-icon>\r\n    <span class=\"ava-textarea__error-text\">{{ error }}</span>\r\n  </div>\r\n\r\n  <!-- Helper Message -->\r\n  <div *ngIf=\"hasHelper\" [id]=\"helperId\" class=\"ava-textarea__helper\">\r\n    <ava-icon\r\n      iconName=\"info\"\r\n      [iconSize]=\"14\"\r\n      class=\"ava-textarea__helper-icon\"\r\n      [cursor]=\"false\"\r\n      [disabled]=\"false\"\r\n    ></ava-icon>\r\n    <span class=\"ava-textarea__helper-text\">{{ helper }}</span>\r\n  </div>\r\n</div>\r\n", "import { ChangeDetectionStrategy, Component, Input, HostListener } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IconComponent } from '../icon/icon.component';\r\n\r\nexport type BadgeState = 'high-priority' | 'medium-priority' | 'low-priority' | 'neutral' | 'information';\r\nexport type BadgeSize = 'large' | 'medium' | 'small';\r\n\r\n@Component({\r\n  selector: 'ava-badges',\r\n  imports: [CommonModule, IconComponent],\r\n  templateUrl: './badges.component.html',\r\n  styleUrls: ['./badges.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class BadgesComponent {\r\n  @Input() state: BadgeState = 'neutral';\r\n  @Input() size: BadgeSize = 'medium';\r\n  @Input() count?: number;\r\n  @Input() iconName?: string;\r\n  @Input() iconColor?: string;\r\n  @Input() iconSize?: number;\r\n\r\n  get displayCount(): string {\r\n    if (!this.count) return '';\r\n    if (this.count > 999) return '999+';\r\n    if (this.count > 99) return '99+';\r\n    if (this.count > 9) return '9+';\r\n    return this.count.toString();\r\n  }\r\n\r\n  get badgeClasses(): string {\r\n    const baseClasses = `badge badge--${this.state} badge--${this.size}`;\r\n    // Add expanded class only for multi-character content\r\n    if (this.count && this.displayCount.length > 1) {\r\n      return `${baseClasses} badge--expanded`;\r\n    }\r\n    return baseClasses;\r\n  }\r\n\r\n  get hasContent(): boolean {\r\n    return !!(this.count || this.iconName);\r\n  }\r\n\r\n  get isSingleDigit(): boolean {\r\n    return this.count !== undefined && this.count >= 0 && this.count <= 9;\r\n  }\r\n\r\n  onKeyPress() {\r\n    // Handle the key press event, e.g., trigger an action or navigate\r\n    console.log('Badge component pressed via keyboard');\r\n    // Add your custom logic here\r\n  }\r\n}\r\n", "<div\r\n  [class]=\"badgeClasses\"\r\n  tabindex=\"0\"\r\n  (keydown.enter)=\"onKeyPress()\"\r\n  (keydown.space)=\"onKeyPress()\"\r\n>\r\n  <ng-container *ngIf=\"hasContent\">\r\n    <!-- Display ava-icon if iconName is provided and no count -->\r\n    <ng-container *ngIf=\"iconName && !count\">\r\n      <ava-icon\r\n        class=\"badge__icon\"\r\n        [iconName]=\"iconName\"\r\n        [iconColor]=\"iconColor!\"\r\n        [iconSize]=\"iconSize!\"\r\n      >\r\n      </ava-icon>\r\n    </ng-container>\r\n    <!-- Display count if provided -->\r\n    <span *ngIf=\"count\" class=\"badge__count\">{{ displayCount }}</span>\r\n  </ng-container>\r\n</div>\r\n", "import { ChangeDetectionStrategy, Component, Input } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { BadgesComponent, BadgeState, BadgeSize } from '../badges/badges.component';\r\n\r\nexport type AvatarSize = 'small' | 'medium' | 'large';\r\nexport type AvatarShape = 'pill' | 'square';\r\nexport type AvatarTextType = 'status' | 'profile';\r\n\r\n@Component({\r\nselector: 'ava-avatars',\r\nimports: [CommonModule, BadgesComponent],\r\ntemplateUrl: './avatars.component.html',\r\nstyleUrl: './avatars.component.scss',\r\nchangeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class AvatarsComponent {\r\n@Input() size: AvatarSize = 'large';\r\n@Input() shape: AvatarShape = 'pill';\r\n@Input() imageUrl: string = '';\r\n@Input() statusText?: string;\r\n@Input() profileText?: string;\r\n@Input() badgeState?: BadgeState;\r\n@Input() badgeSize?: BadgeSize;\r\n@Input() badgeCount?: number;\r\n\r\nget avatarClasses(): string {\r\nreturn `avatar avatar--${this.size} avatar--${this.shape}`;\r\n}\r\n\r\nget hasBadge(): boolean {\r\nreturn !!(this.badgeState || this.badgeCount);\r\n}\r\n\r\nget hasStatusText(): boolean {\r\nreturn !!this.statusText;\r\n}\r\n\r\nget hasProfileText(): boolean {\r\nreturn !!this.profileText;\r\n}\r\n\r\nget hasAnyText(): boolean {\r\nreturn this.hasStatusText || this.hasProfileText;\r\n}\r\n}", "<div class=\"avatar-container\">\r\n  <div class=\"avatar-wrapper\">\r\n    <div\r\n      [class]=\"avatarClasses\"\r\n      [style.background-image]=\"imageUrl ? 'url(' + imageUrl + ')' : 'none'\"\r\n    >\r\n      <ava-badges\r\n        *ngIf=\"hasBadge\"\r\n        [state]=\"badgeState!\"\r\n        [size]=\"badgeSize!\"\r\n        [count]=\"badgeCount\"\r\n        class=\"avatar-badge\"\r\n      >\r\n      </ava-badges>\r\n    </div>\r\n    <!-- Text labels - can have both status and profile -->\r\n    <div *ngIf=\"hasAnyText\" class=\"avatar-text-container\">\r\n      <div *ngIf=\"hasStatusText\" class=\"avatar-text avatar-text--status\">\r\n        {{ statusText }}\r\n      </div>\r\n      <div *ngIf=\"hasProfileText\" class=\"avatar-text avatar-text--profile\">\r\n        {{ profileText }}\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n", "import { CommonModule } from '@angular/common';\r\nimport { Component, Input } from '@angular/core';\r\n\r\nexport type SpinnerType = 'circular' | 'dotted' | 'partial' |'gradient' | 'dashed' | 'double';\r\nexport type SpinnerSize = 'sm' | 'md' | 'lg' | 'xl';\r\n\r\n@Component({\r\n  selector: 'ava-spinner',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './spinner.component.html',\r\n  styleUrls: ['./spinner.component.scss']\r\n})\r\nexport class SpinnerComponent {\r\n  @Input() type: SpinnerType = 'circular';\r\n  @Input() size: SpinnerSize = 'md';\r\n  @Input() className: string = '';\r\n  @Input() animation:boolean = true;\r\n  @Input() color: 'primary' | 'secondary' | 'success' | 'warning' | 'danger'|'purple' = 'primary';\r\n  @Input() progressIndex?: number;\r\n\r\n  // Size mapping (reused)\r\n  get sizeClass(): string {\r\n    const sizeMap: Record<SpinnerSize, string> = {\r\n      sm: 'size-sm',\r\n      md: 'size-md',\r\n      lg: 'size-lg',\r\n      xl: 'size-xl'\r\n    };\r\n    return sizeMap[this.size];\r\n  }\r\n\r\n  // Check if the type is a simple spinner type\r\n  get isStandardSpinner(): boolean {\r\n    return ['circular', 'dotted', 'partial','dashed'].includes(this.type);\r\n  }\r\n  \r\n  get progressClass(): string {\r\n  if (this.progressIndex === undefined) return '';\r\n  const rounded = Math.round(this.progressIndex);\r\n\r\n  if (rounded <= 25) return 'rotate-25';\r\n  if (rounded <= 50) return 'rotate-50';\r\n  if (rounded <= 75) return 'rotate-75';\r\n  return 'rotate-100';\r\n}\r\n\r\n}\r\n\r\n", "<div class=\"ava-spinner-container\">\r\n <div *ngIf=\"isStandardSpinner\"\r\n     class=\"spinner\"\r\n     [ngClass]=\"[\r\n        type,\r\n        sizeClass,\r\n        animation ? 'animated' : '',\r\n        color,\r\n        progressIndex !== undefined ? 'progress-spinner' : '',\r\n        progressClass\r\n     ]\">\r\n</div>\r\n\r\n  <div *ngIf=\"type === 'gradient'\" class=\"spinner-wrapper\" [ngClass]=\"sizeClass\">\r\n    <div class=\"spinner gradient\" [ngClass]=\"[sizeClass, animation ? 'animated' : '',color]\">\r\n      <div class=\"gradient-inner\"></div>\r\n    </div>\r\n  </div>\r\n\r\n  <div *ngIf=\"type === 'double'\" class=\"spinner-wrapper\" [ngClass]=\"sizeClass\">\r\n    <div class=\"spinner double-outer\" [ngClass]=\"[sizeClass, animation ? 'animated' : '',color]\"></div>\r\n    <div class=\"spinner double-inner\" [ngClass]=\"[sizeClass, animation ? 'animated' : '',color]\"></div>\r\n  </div>\r\n</div>", "import { ChangeDetectionStrategy, Component, Input } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n@Component({\r\n  selector: 'ava-card',\r\n  imports: [CommonModule],\r\n  templateUrl: './card.component.html',\r\n  styleUrl: './card.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class CardComponent {\r\n\r\n  @Input() heading = '';\r\n  @Input() content = '';\r\n\r\n}\r\n\r\n", "<div class=\"ava-card-container\" [attr.role]=\"'listitem'\">\r\n    <div class=\"ava-card card\">\r\n        <div class=\"card-wrapper\">\r\n            <div class=\"card-header\">\r\n                <ng-content select=\"div[header]\"></ng-content>\r\n            </div>\r\n\r\n            <div class=\"card-content\">\r\n                <ng-content select=\"div[content]\"></ng-content>\r\n                <ng-content select=\"div[contentFooter]\"></ng-content>\r\n            </div>\r\n\r\n            <div class=\"card-footer\">\r\n                <ng-content select=\"div[footer]\"></ng-content>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>", "import { ChangeDetectionStrategy, Component, Input } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\n@Component({\r\n  selector: 'ava-feature-card',\r\n  imports: [CommonModule],\r\n  templateUrl: './feature-card.component.html',\r\n  styleUrl: './feature-card.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class FeatureCardComponent {\r\n  @Input() heading = '';\r\n  @Input() content = '';\r\n  @Input() variant: 'blue' | 'red' | 'purple' | 'green' | 'orange' | 'teal' = 'blue';\r\n}\r\n", "<div class=\"ava-featured-card-container\" [ngClass]=\"variant\" [attr.role]=\"'listitem'\">\r\n    <div class=\"ava-feature-card\">\r\n        <div class=\"card-content-wrapper\">\r\n            <div class=\"card-header c-card-header \">\r\n                <ng-content select=\"div[header]\"></ng-content>\r\n            </div>\r\n\r\n            <div class=\"card-content\">\r\n                <ng-content select=\"div[content]\"></ng-content>\r\n            </div>\r\n\r\n            <div class=\"card-footer\">\r\n                <ng-content select=\"div[footer]\"></ng-content>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>", "import { ChangeDetectionStrategy, Component, Input } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n@Component({\r\n  selector: 'ava-advanced-card',\r\n  imports: [CommonModule],\r\n  templateUrl: './advanced-card.component.html',\r\n  styleUrl: './advanced-card.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class AdvancedCardComponent {\r\n\r\n}\r\n", "<div class=\"ava-advanced-card-container pricing-container\" [attr.role]=\"'listitem'\">\r\n  <div class=\"ava-advanced-card card\">\r\n    <div class=\"card-content\">       \r\n          <div class=\"card-header\">\r\n              <ng-content select=\"div[header]\"></ng-content>\r\n          </div>\r\n\r\n          <div class=\"card-content\">\r\n              <ng-content select=\"div[content]\"></ng-content>\r\n          </div>\r\n\r\n          <div class=\"card-footer\">\r\n              <ng-content select=\"div[footer]\"></ng-content>\r\n          </div>\r\n    </div>\r\n\r\n  </div>\r\n</div>", "import { CommonModule } from '@angular/common';\r\nimport {\r\n  ChangeDetectionStrategy,\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  Output\r\n} from '@angular/core';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IconComponent } from '../../components/icon/icon.component';\r\nimport { ButtonComponent } from '../../components/button/button.component';\r\n\r\n@Component({\r\n  selector: 'ava-popup',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, IconComponent, ButtonComponent],\r\n  templateUrl: './popup.component.html',\r\n  styleUrl: './popup.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class PopupComponent {\r\n  // Controls popup visibility\r\n  @Input() show = false;\r\n\r\n  // Message ALignment \r\n  @Input() messageAlignment: 'left' | 'center' | 'right' = 'center';\r\n\r\n  // Popup Position\r\n  @Input() position: 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top' | 'bottom' = 'center';\r\n\r\n  // Title and message content\r\n  @Input() title = 'SUCCESS';\r\n  @Input() message = 'Action completed successfully.';\r\n  @Input() showTitle = true;\r\n\r\n  // Header icon configuration\r\n  @Input() showHeaderIcon = true;\r\n  @Input() headerIconName = 'circle-check';\r\n  @Input() iconColor = 'green';\r\n  @Input() iconSize = 70;\r\n\r\n  // Close button (top-right corner) configuration\r\n  @Input() showClose = true;\r\n  @Input() closeIconName = 'x';\r\n  @Input() closeIconColor = '#a2a2a2';\r\n  @Input() closeIconSize = 24;\r\n\r\n  // Inline message (icon + text row below title)\r\n  @Input() showInlineMessage = false;\r\n  @Input() inlineIconName = 'badge-check';\r\n  @Input() inlineIconSize = 40;\r\n  @Input() inlineIconColor = 'green';\r\n  @Input() inlineMessage = '';\r\n\r\n  // Popup width input — accepts string or number (converted to px)\r\n  private _popupWidth: string = '400px';\r\n\r\n  @Input()\r\n  set popupWidth(value: string | number) {\r\n    this._popupWidth = typeof value === 'number' ? `${value}px` : value;\r\n  }\r\n  get popupWidth(): string {\r\n    return this._popupWidth;\r\n  }\r\n\r\n  // Button visibility controls\r\n  @Input() showConfirm = false;\r\n  @Input() showCancel = false;\r\n\r\n  // Cancel button configuration\r\n  @Input() cancelButtonLabel = 'Cancel';\r\n  @Input() cancelButtonSize: 'small' | 'medium' | 'large' = 'small';\r\n  @Input() cancelButtonVariant: 'primary' | 'secondary' = 'secondary';\r\n  @Input() cancelButtonBackground = '';\r\n\r\n  // Confirm button configuration\r\n  @Input() confirmButtonLabel = 'Confirm';\r\n  @Input() confirmButtonSize: 'small' | 'medium' | 'large' = 'small';\r\n  @Input() confirmButtonVariant: 'primary' | 'secondary' = 'primary';\r\n  @Input() confirmButtonBackground = '';\r\n\r\n  // Output events\r\n  @Output() confirm = new EventEmitter<void>(); // Emit on confirm\r\n  @Output() cancel = new EventEmitter<void>();  // Emit on cancel\r\n  @Output() closed = new EventEmitter<void>();  // Emit when popup is closed\r\n\r\n  // Called when confirm button is clicked\r\n  onConfirm(): void {\r\n    this.confirm.emit();\r\n    this.closePopup();\r\n  }\r\n\r\n  // Called when cancel button is clicked\r\n  onCancel(): void {\r\n    this.cancel.emit();\r\n    this.closePopup();\r\n  }\r\n\r\n  // Closes the popup and emits the closed event\r\n  closePopup(): void {\r\n    this.closed.emit();\r\n  }\r\n\r\n  /**\r\n   * Splits multiline message content using <br> tags and trims each line.\r\n   * Used in the template to support line breaks.\r\n   */\r\n  getMessageLines(): string[] {\r\n    return this.message.split(/<br\\s*\\/?>/i).map(line => line.trim());\r\n  }\r\n}\r\n", "<div class=\"popup-backdrop\" *ngIf=\"show\" role=\"dialog\" [ngClass]=\"'popup-position-' + position\" aria-modal=\"true\"\r\n    [attr.aria-labelledby]=\"showTitle ? 'popup-title' : null\"\r\n    [attr.aria-describedby]=\"message ? 'popup-description' : null\">\r\n\r\n    <div class=\"popup-container\" [ngStyle]=\"{ width: popupWidth }\">\r\n\r\n        <ava-icon *ngIf=\"showClose\" class=\"close-btn\" [iconName]=\"closeIconName\" [iconSize]=\"closeIconSize\"\r\n            [iconColor]=\"closeIconColor\" [cursor]=\"true\" (click)=\"closePopup()\" role=\"button\" aria-label=\"Close popup\"\r\n            tabindex=\"0\">\r\n        </ava-icon>\r\n\r\n        <div *ngIf=\"showHeaderIcon\">\r\n            <ava-icon [iconName]=\"headerIconName\" [iconSize]=\"iconSize\" [iconColor]=\"iconColor\" aria-hidden=\"true\">\r\n            </ava-icon>\r\n        </div>\r\n\r\n        <h3 class=\"popup-title\" id=\"popup-title\" *ngIf=\"showTitle\">\r\n            {{ title }}\r\n        </h3>\r\n\r\n        <div class=\"popup-inline-message single-line\" *ngIf=\"showInlineMessage\">\r\n            <span class=\"inline-icon\">\r\n                <ava-icon [iconName]=\"inlineIconName\" [iconSize]=\"inlineIconSize\" [iconColor]=\"inlineIconColor\"\r\n                    aria-hidden=\"true\">\r\n                </ava-icon>\r\n            </span>\r\n            <span class=\"inline-text\">{{ inlineMessage }}</span>\r\n        </div>\r\n\r\n        <p *ngIf=\"message\" class=\"popup-message\" id=\"popup-description\" [ngClass]=\"{\r\n    'left-align': messageAlignment === 'left',\r\n    'center-align': messageAlignment === 'center',\r\n    'right-align': messageAlignment === 'right'\r\n  }\">\r\n            <ng-container *ngFor=\"let line of getMessageLines(); let last = last\">\r\n                {{ line }}<br *ngIf=\"!last\" />\r\n            </ng-container>\r\n        </p>\r\n\r\n        <!-- Slot for custom content -->\r\n        <ng-content></ng-content>\r\n\r\n        <div class=\"popup-actions\" *ngIf=\"showCancel || showConfirm\">\r\n\r\n            <ava-button *ngIf=\"showCancel\" [label]=\"cancelButtonLabel\" [size]=\"cancelButtonSize\"\r\n                [variant]=\"cancelButtonVariant\" [background]=\"cancelButtonBackground\" (click)=\"onCancel()\">\r\n            </ava-button>\r\n\r\n            <ava-button *ngIf=\"showConfirm\" [label]=\"confirmButtonLabel\" [size]=\"confirmButtonSize\"\r\n                [variant]=\"confirmButtonVariant\" [background]=\"confirmButtonBackground\" (click)=\"onConfirm()\">\r\n            </ava-button>\r\n\r\n        </div>\r\n\r\n    </div>\r\n</div>", "import { ChangeDetectionStrategy, Component, Input, ViewEncapsulation } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\n@Component({\r\n  selector: 'ava-link',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './link.component.html',\r\n  styleUrls: ['./link.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class LinkComponent {\r\n  @Input() label = 'Action Link';\r\n  @Input() color: 'success' | 'warning' | 'danger' | 'info' | 'default' | 'primary' | string = 'default';\r\n  @Input() size: 'small' | 'medium' | 'large' = 'medium';\r\n  @Input() underline: boolean = false;\r\n\r\n  isHexColor(color: string): boolean {\r\n    return /^#([0-9A-F]{3}){1,2}$/i.test(color);\r\n  }\r\n}\r\n\r\n", "<a\r\n class=\"action-link\"\r\n [ngClass]=\"[color, size, underline ? 'underline' : '']\"\r\n [ngStyle]=\"isHexColor(color) ? {'color': color} : {}\"\r\n>\r\n {{ label }}\r\n</a>\r\n\r\n", "import { Component, Input, Output, EventEmitter } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IconComponent } from '../icon/icon.component';\r\n\r\n/**\r\n * AvaTagComponent: Modern, accessible, and highly customizable tag/chip component.\r\n * Supports filled/outlined, color variants, pill/rect, removable, icons, avatars, sizes, and custom styles.\r\n */\r\n@Component({\r\n  selector: 'ava-tag',\r\n  templateUrl: './tags.component.html',\r\n  styleUrls: ['./tags.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, IconComponent],\r\n})\r\nexport class AvaTagComponent {\r\n  /** Tag label text */\r\n  @Input() label!: string;\r\n  /** Color variant */\r\n  @Input() color: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info' | 'custom' = 'default';\r\n  /** Outlined or filled */\r\n  @Input() variant: 'filled' | 'outlined' = 'filled';\r\n  /** Tag size */\r\n  @Input() size: 'sm' | 'md' | 'lg' = 'md';\r\n  /** Pill/rounded shape */\r\n  @Input() pill = false;\r\n  /** Removable (shows close icon) */\r\n  @Input() removable = false;\r\n  /** Disabled state */\r\n  @Input() disabled = false;\r\n  /** Icon name (ava-icon) */\r\n  @Input() icon?: string;\r\n  /** Icon position (left only, close always right) */\r\n  @Input() iconPosition: 'start' | 'end' = 'start';\r\n  /** Avatar: image URL or initials */\r\n  @Input() avatar?: string;\r\n  /** Custom style object for CSS vars */\r\n  @Input() customStyle?: Record<string, string>;\r\n  /** Custom class for tag */\r\n  @Input() customClass?: string;\r\n  /** Custom icon color */\r\n  @Input() iconColor?: string;\r\n\r\n  /** Emits when tag is removed (close icon) */\r\n  @Output() removed = new EventEmitter<void>();\r\n  /** Emits when tag is clicked (if handler provided) */\r\n  @Output() clicked = new EventEmitter<void>();\r\n\r\n  /** True if tag is clickable (handler attached and not disabled) */\r\n  get clickable(): boolean {\r\n    return this.clicked.observers.length > 0 && !this.disabled;\r\n  }\r\n\r\n  /** Remove handler (close icon) */\r\n  onRemove(event: Event) {\r\n    event.stopPropagation();\r\n    if (!this.disabled) {\r\n      this.removed.emit();\r\n    }\r\n  }\r\n\r\n  /** Click handler (entire tag) */\r\n  onClick() {\r\n    if (this.clickable) {\r\n      this.clicked.emit();\r\n    }\r\n  }\r\n} ", "<ng-container>\r\n  <span\r\n    class=\"ava-tag\"\r\n    [ngClass]=\"[\r\n      'ava-tag',\r\n      'ava-tag--' + color,\r\n      'ava-tag--' + variant,\r\n      'ava-tag--' + size,\r\n      pill ? 'ava-tag--pill' : '',\r\n      disabled ? 'ava-tag--disabled' : '',\r\n      customClass || '',\r\n      clickable ? 'ava-tag--clickable' : ''\r\n    ]\"\r\n    [ngStyle]=\"customStyle\"\r\n    [attr.tabindex]=\"clickable ? 0 : null\"\r\n    [attr.role]=\"clickable ? 'button' : null\"\r\n    [attr.aria-disabled]=\"disabled ? 'true' : null\"\r\n    (click)=\"onClick()\"\r\n    (keyup.enter)=\"onClick()\"\r\n  >\r\n    <ng-container *ngIf=\"avatar\">\r\n      <span class=\"ava-tag__avatar\" *ngIf=\"avatar && avatar.startsWith('http')\">\r\n        <img [src]=\"avatar\" alt=\"avatar\" />\r\n      </span>\r\n      <span\r\n        class=\"ava-tag__avatar ava-tag__avatar--initials\"\r\n        *ngIf=\"avatar && !avatar.startsWith('http')\"\r\n      >\r\n        {{ avatar }}\r\n      </span>\r\n    </ng-container>\r\n    <ng-container *ngIf=\"icon && iconPosition === 'start'\">\r\n      <ava-icon\r\n        [iconName]=\"icon || ''\"\r\n        [iconSize]=\"16\"\r\n        [iconColor]=\"iconColor || 'currentColor'\"\r\n        [cursor]=\"false\"\r\n        [disabled]=\"disabled\"\r\n        class=\"ava-tag__icon ava-tag__icon--start\"\r\n      ></ava-icon>\r\n    </ng-container>\r\n    <span class=\"ava-tag__label\">{{ label }}</span>\r\n    <button\r\n      *ngIf=\"removable\"\r\n      type=\"button\"\r\n      class=\"ava-tag__remove-btn\"\r\n      [disabled]=\"disabled\"\r\n      tabindex=\"0\"\r\n      aria-label=\"Remove tag\"\r\n      (click)=\"onRemove($event)\"\r\n      (keyup.enter)=\"onRemove($event)\"\r\n    >\r\n      <ava-icon\r\n        iconName=\"x\"\r\n        [iconSize]=\"16\"\r\n        [iconColor]=\"iconColor || 'currentColor'\"\r\n        [cursor]=\"true\"\r\n        [disabled]=\"disabled\"\r\n        class=\"ava-tag__remove\"\r\n      ></ava-icon>\r\n    </button>\r\n  </span>\r\n</ng-container>\r\n", "import { ChangeDetectionStrategy, Component, Input, input, TemplateRef } from '@angular/core';\r\nimport { CardComponent } from '../../components/card/card.component';\r\nimport { ButtonComponent } from '../../components/button/button.component';\r\nimport { CommonModule, NgFor, NgIf } from '@angular/common';\r\nimport { AvaTagComponent } from '../../components/tags/tags.component';\r\nimport { IconComponent } from '../../components/icon/icon.component';\r\n\r\nexport interface CardData {\r\n  header?: {\r\n    title: string,\r\n    iconName: string,\r\n    viewAll: boolean\r\n  };\r\n  contents?: [\r\n    { session1: any }\r\n  ];\r\n  footer?: {};\r\n}\r\n\r\n@Component({\r\n  selector: 'ava-approval-card',\r\n  imports: [CommonModule, NgIf, NgFor, CardComponent, AvaTagComponent, IconComponent],\r\n  templateUrl: './approval-card.component.html',\r\n  styleUrl: './approval-card.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class ApprovalCardComponent {\r\n  @Input() cardData!: any;\r\n  @Input() height: string = '300px';\r\n  @Input() contentTemplate!: TemplateRef<any>;\r\n  @Input() contentsBackground: string = '';\r\n  @Input() cardContainerBackground: string = '';\r\n\r\n\r\n\r\n}\r\n", "<div class=\"ava-console-approval-card-container\" [style.background-color]=\"cardContainerBackground\">\r\n    <ava-card>\r\n        <div header *ngIf=\"cardData?.header\">\r\n            <h2 *ngIf=\"cardData?.header?.title\">{{cardData?.header?.title}}</h2>\r\n        </div>\r\n        <div [style.height.px]=\"height?height:null\" class=\"content-wrapper\" content *ngIf=\"cardData?.contents\">\r\n            <div class=\"contents\" *ngFor=\"let each of cardData?.contents; let i= index\"\r\n                [style.background-color]=\"contentsBackground\">\r\n                <ng-content select=\"div[contentFooter]\"></ng-content>\r\n\r\n                <div class=\"box c-header-wrapper\" style=\"display: flex; justify-content: space-between; width: 100%;\">\r\n                    <div class=\"head-left\">\r\n                        <p *ngIf=\"each?.session1?.title\">{{each?.session1?.title}} </p>\r\n                    </div>\r\n                    <div class=\"head-right\">\r\n                        <ava-tag *ngFor=\"let eachLabel of each?.session1?.labels\" [label]=\"eachLabel.name\"\r\n                            [color]=\"eachLabel.color\" size=\"sm\"></ava-tag>\r\n                        <ava-icon [iconColor]=\"'green'\" iconSize=\"20\" iconName=\"ellipsis-vertical\"></ava-icon>\r\n                    </div>\r\n\r\n                </div>\r\n                <div class=\"box label-wrapper\">\r\n                    <ava-tag *ngFor=\"let eachLabel of each?.session2\" [label]=\"eachLabel.name\" [color]=\"eachLabel.color\"\r\n                        size=\"sm\"></ava-tag>\r\n                </div>\r\n\r\n                <div class=\"box info-wrapper\">\r\n                    <div [ngClass]=\"{'f': i==0}\" *ngFor=\"let eachLabel of each?.session3; let i=index\">\r\n                        <ava-icon iconSize=\"20\" [iconName]=\"eachLabel.iconName\"></ava-icon>\r\n                        <span>{{eachLabel.label}}</span>\r\n                    </div>\r\n                </div>\r\n                <div class=\"box footer-wrapper\" *ngIf=\" each?.session4\">\r\n                    <ng-container *ngTemplateOutlet=\"contentTemplate;context: { index: i , label:each.session4 }\">\r\n                    </ng-container>\r\n                </div>\r\n\r\n\r\n            </div>\r\n        </div>\r\n\r\n    </ava-card>\r\n</div>", "import { ChangeDetectionStrategy, Component, Input } from '@angular/core';\r\nimport { CardComponent } from '../../components/card/card.component';\r\nimport { CommonModule } from '@angular/common';\r\n\r\n@Component({\r\n  selector: 'ava-image-card',\r\n  standalone: true,\r\n  imports: [CardComponent, CommonModule],\r\n  templateUrl: './image-card.component.html',\r\n  styleUrl: './image-card.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class ImageCardComponent {\r\n  @Input() imageUrl: string = '';\r\n  @Input() name: string = '';\r\n  @Input() title: string = 'Welcome to Console 🚀';\r\n  @Input() imagePosition: 'left' | 'right' | 'top' | 'bottom' = 'left' //default;\r\n  getWrapperClass() {\r\n    if (this.imagePosition === 'top' || this.imagePosition === 'bottom') {\r\n      return 'img-card-wrapper-vertical';\r\n    }\r\n    return 'img-card-wrapper-horizontal';\r\n  }\r\n}\r\n", "<ava-card>\r\n    <div content>\r\n        <div [ngClass]=\"getWrapperClass()\">\r\n            <ng-container [ngSwitch]=\"imagePosition || 'left'\">\r\n                <!-- Image first for left or top -->\r\n                <ng-container *ngSwitchCase=\"'left'\">\r\n                    <div class=\"left-wrapper\">\r\n                        <img [src]=\"imageUrl\" />\r\n                    </div>\r\n                    <div class=\"right-wrapper\">\r\n                        <p>{{ title }}</p>\r\n                        <span>{{ name }}</span>\r\n                    </div>\r\n                </ng-container>\r\n\r\n                <ng-container *ngSwitchCase=\"'right'\">\r\n                    <div class=\"right-wrapper\">\r\n                        <p>{{ title }}</p>\r\n                        <span>{{ name }}</span>\r\n                    </div>\r\n                    <div class=\"left-wrapper\">\r\n                        <img [src]=\"imageUrl\" />\r\n                    </div>\r\n                </ng-container>\r\n\r\n                <ng-container *ngSwitchCase=\"'top'\">\r\n                    <div class=\"top-wrapper\">\r\n                        <img [src]=\"imageUrl\" />\r\n                    </div>\r\n                    <div class=\"bottom-wrapper\">\r\n                        <p>{{ title }}</p>\r\n                        <span>{{ name }}</span>\r\n                    </div>\r\n                </ng-container>\r\n\r\n                <ng-container *ngSwitchCase=\"'bottom'\">\r\n                    <div class=\"bottom-wrapper\">\r\n                        <p>{{ title }}</p>\r\n                        <span>{{ name }}</span>\r\n                    </div>\r\n                    <div class=\"top-wrapper\">\r\n                        <img [src]=\"imageUrl\" />\r\n                    </div>\r\n                </ng-container>\r\n            </ng-container>\r\n        </div>\r\n    </div>\r\n</ava-card>", "import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, ViewEncapsulation } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IconComponent } from '../../components/icon/icon.component';\r\nimport { FeatureCardComponent } from '../../components/feature-card/feature-card.component';\r\nimport { CardComponent } from \"../../components/card/card.component\";\r\n\r\n@Component({\r\n  selector: 'ava-text-card',\r\n  standalone: true,\r\n  imports: [CommonModule, IconComponent, CardComponent, CardComponent],\r\n  templateUrl: './text-card.component.html',\r\n  styleUrls: ['./text-card.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n  encapsulation: ViewEncapsulation.None\r\n})\r\nexport class TextCardComponent {\r\n  @Input() iconName = 'trending-up';\r\n  @Input() title: string = '';\r\n  @Input() value: string | number = '';\r\n  @Input() description: string = '';\r\n  @Input() width = 0;\r\n  @Input() type: 'default' | 'create' | 'prompt' = 'default';\r\n  @Input() iconColor: string = ''\r\n  @Input() userCount: number = 0;\r\n  @Input() promptName: string = '';\r\n  @Input() name: string = '';\r\n  @Input() date: string = ''\r\n  @Input() iconList: any = []\r\n  @Output() cardClick = new EventEmitter<void>();\r\n  @Output() iconClick = new EventEmitter<void>();\r\n\r\n  onCardClick() {\r\n    this.cardClick.emit();\r\n  }\r\n  iconClicked(icon: any) {\r\n    this.iconClick.emit(icon);\r\n  }\r\n}\r\n", "<div class=\"ava-text-card-container\" [style.width.px]=\"width > 0 ? width : null\">\r\n    <!-- Default Type -->\r\n    <ng-container *ngIf=\"type === 'default'\">\r\n        <div class=\"default\">\r\n            <ava-card>\r\n                <div header class=\"text-card-header\">\r\n                    <div class=\"icon-circle\">\r\n                        <ava-icon [iconName]=\"iconName\" [iconSize]=\"24\"></ava-icon>\r\n                    </div>\r\n                    <div>\r\n                        <h3>{{ title }}</h3>\r\n                    </div>\r\n                </div>\r\n\r\n                <div content>\r\n                    <h1>{{ value }}</h1>\r\n                </div>\r\n\r\n                <div footer>\r\n                    <p>{{ description }}</p>\r\n                </div>\r\n            </ava-card>\r\n        </div>\r\n\r\n    </ng-container>\r\n\r\n    <!-- Create Type -->\r\n    <ng-container *ngIf=\"type === 'create'\">\r\n        <div class=\"create-type\" (click)=\"onCardClick()\">\r\n            <ava-card>\r\n                <div content class=\"card-content\">\r\n                    <div class=\"icon-container\">\r\n                        <ava-icon [iconName]=\"'plus'\" [iconColor]=\"iconColor\" [iconSize]=\"24\"></ava-icon>\r\n                    </div>\r\n                    <div>\r\n                        <h3>{{ title }}</h3>\r\n                    </div>\r\n                </div>\r\n            </ava-card>\r\n        </div>\r\n    </ng-container>\r\n\r\n    <!-- Prompt Type -->\r\n    <ng-container *ngIf=\"type === 'prompt'\">\r\n        <div class=\"prompt-type\" (click)=\"onCardClick()\">\r\n            <ava-card>\r\n                <div header class=\"text-card-header\">\r\n                    <div class=\"top-icons\">\r\n                        <span>\r\n                            <ava-icon [iconName]=\"'wrench'\" [iconColor]=\"iconColor\" [iconSize]=\"15\"></ava-icon> Tool\r\n                        </span>\r\n                        <span>\r\n                            <ava-icon [iconName]=\"'users'\" [iconColor]=\"iconColor\" [iconSize]=\"15\"></ava-icon> {{\r\n                            userCount }}\r\n                        </span>\r\n                    </div>\r\n                    <div>\r\n                        <h3>{{ title }}</h3>\r\n                    </div>\r\n                </div>\r\n\r\n                <div content class=\"card-content\">\r\n                    <p class=\"description\">{{ description }}</p>\r\n                </div>\r\n\r\n                <div footer class=\"footer\">\r\n                    <div class=\"name-date-container\">\r\n                        <p>\r\n                            <span><ava-icon [iconName]=\"'user'\" [iconSize]=\"10\" [iconColor]=\"iconColor\"></ava-icon>\r\n                            </span> {{ name }}\r\n                        </p>\r\n\r\n                        <p>\r\n                            <span>\r\n                                <ava-icon [iconName]=\"'calendar-days'\" [iconSize]=\"10\"\r\n                                    [iconColor]=\"iconColor\"></ava-icon>\r\n                            </span>\r\n                            {{ date }}\r\n                        </p>\r\n\r\n                    </div>\r\n                    <div class=\"action-icon-container\">\r\n                        <ng-container *ngFor=\"let icon of iconList\">\r\n\r\n                            <span [ngClass]=\"{'play-icon-wrapper': icon?.name === 'play' }\">\r\n                                <ava-icon (userClick)=\"iconClicked(icon)\" [cursor]=\"icon?.cursor\"\r\n                                    [iconName]=\"icon?.iconName\" [iconSize]=\"20\" iconColor=\"#6489BF\"></ava-icon>\r\n                            </span>\r\n                        </ng-container>\r\n                    </div>\r\n                </div>\r\n            </ava-card>\r\n        </div>\r\n    </ng-container>\r\n</div>", "import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter, OnInit, OnDestroy, HostListener, ElementRef, forwardRef, ChangeDetectorRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { CheckboxComponent } from '../checkbox/checkbox.component';\r\nimport { IconComponent } from '../icon/icon.component';\r\n\r\nexport interface DropdownOption {\r\n  name: string;\r\n  value: string | number;\r\n  icon?: string; // Optional icon for the option\r\n  disabled?: boolean; // Optional disabled state for individual options\r\n}\r\n\r\n@Component({\r\n  selector: 'ava-dropdown',\r\n  imports: [CommonModule, FormsModule, CheckboxComponent, IconComponent],\r\n  templateUrl: './dropdown.component.html',\r\n  styleUrl: './dropdown.component.scss',\r\n  // changeDetection: ChangeDetectionStrategy.OnPush, // Temporarily disabled for debugging\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => DropdownComponent),\r\n      multi: true\r\n    }\r\n  ]\r\n})\r\nexport class DropdownComponent implements OnInit, OnDestroy {\r\n  @Input() dropdownTitle: string = 'Select a Category';\r\n  @Input() options: DropdownOption[] = [];\r\n  @Input() suboptions: { [key: string]: DropdownOption[] } = {};\r\n  @Input() checkboxOptions: string[] = [];\r\n  @Input() iconOptions: string[] = [];\r\n  @Input() search: boolean = false;\r\n  @Input() enableSearch: boolean = false;\r\n  @Input() selectedValue: string = '';\r\n  @Input() singleSelect: boolean = false;\r\n  @Input() dropdownIcon: string = 'chevron-down'; // Icon for dropdown toggle\r\n  @Input() disabled: boolean = false; // Disable entire dropdown\r\n\r\n  @Output() selectionChange = new EventEmitter<any>();\r\n  @Output() valueChange = new EventEmitter<any>();\r\n\r\n  isOpen = false;\r\n  searchTerm = '';\r\n  selectedOptions: DropdownOption[] = [];\r\n  filteredOptions: DropdownOption[] = [];\r\n  expandedOption: string | null = null;\r\n\r\n  // Keyboard navigation properties\r\n  focusedOptionIndex = -1;\r\n  focusedSubOptionIndex = -1;\r\n  isNavigatingSubOptions = false;\r\n\r\n  // Static property to track all dropdown instances\r\n  private static allDropdowns: DropdownComponent[] = [];\r\n\r\n  value = '';\r\n  constructor(\r\n    private elementRef: ElementRef,\r\n    private cdr: ChangeDetectorRef\r\n  ) {\r\n    // Add this instance to the static array\r\n    DropdownComponent.allDropdowns.push(this);\r\n  }\r\n  // ControlValueAccessor \r\n  private onChange = (value: any) => { };\r\n  private onTouched = () => { };\r\n\r\n  // Required by ControlValueAccessor\r\n  writeValue(obj: any): void {\r\n    this.value = obj;\r\n    // Optional: update internal state/UI if needed\r\n  }\r\n\r\n  registerOnChange(fn: any): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: any): void {\r\n    this.onTouched = fn;\r\n  }\r\n  //\r\n\r\n  ngOnInit() {\r\n    this.filteredOptions = [...this.options];\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    // Remove this instance from the static array\r\n    const index = DropdownComponent.allDropdowns.indexOf(this);\r\n    if (index > -1) {\r\n      DropdownComponent.allDropdowns.splice(index, 1);\r\n    }\r\n  }\r\n\r\n  // Static method to close all other dropdowns except the current one\r\n  private static closeAllDropdownsExcept(currentDropdown: DropdownComponent) {\r\n    console.log('Closing other dropdowns, total dropdowns:', DropdownComponent.allDropdowns.length);\r\n    DropdownComponent.allDropdowns.forEach(dropdown => {\r\n      if (dropdown !== currentDropdown && dropdown.isOpen) {\r\n        console.log('Closing dropdown');\r\n        dropdown.isOpen = false;\r\n        dropdown.expandedOption = null;\r\n        dropdown.resetFocusStates();\r\n        // Trigger change detection for the closed dropdown\r\n        dropdown.cdr.detectChanges();\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleDropdown() {\r\n    // Don't toggle if dropdown is disabled\r\n    if (this.disabled) {\r\n      return;\r\n    }\r\n\r\n    console.log('Toggle dropdown called, current state:', this.isOpen);\r\n    if (this.isOpen) {\r\n      // Closing dropdown\r\n      this.isOpen = false;\r\n      this.expandedOption = null;\r\n      this.resetFocusStates();\r\n    } else {\r\n      // Opening dropdown - this will automatically close others\r\n      console.log('Opening dropdown, will close others first');\r\n      DropdownComponent.closeAllDropdownsExcept(this);\r\n      this.isOpen = true;\r\n    }\r\n  }\r\n\r\n  closeDropdown() {\r\n    this.isOpen = false;\r\n    this.searchTerm = '';\r\n    this.filteredOptions = [...this.options];\r\n    this.expandedOption = null;\r\n    this.resetFocusStates();\r\n  }\r\n\r\n  private resetFocusStates() {\r\n    this.focusedOptionIndex = -1;\r\n    this.focusedSubOptionIndex = -1;\r\n    this.isNavigatingSubOptions = false;\r\n  }\r\n\r\n  onSearch() {\r\n    if (!this.searchTerm.trim()) {\r\n      this.filteredOptions = [...this.options];\r\n      return;\r\n    }\r\n\r\n    const searchLower = this.searchTerm.toLowerCase();\r\n    this.filteredOptions = this.options.filter(option => {\r\n      // Check if main option matches\r\n      const mainOptionMatches = option.name.toLowerCase().includes(searchLower);\r\n\r\n      // Check if any sub-option matches\r\n      const subOptionsMatch = this.suboptions[option.name]?.some(subOption =>\r\n        subOption.name.toLowerCase().includes(searchLower)\r\n      ) || false;\r\n\r\n      return mainOptionMatches || subOptionsMatch;\r\n    });\r\n  }\r\n\r\n  selectOption(option: DropdownOption) {\r\n    // Don't select if option is disabled or dropdown is disabled\r\n    if (option.disabled || this.disabled) {\r\n      return;\r\n    }\r\n\r\n    if (this.checkboxOptions.includes(option.name)) {\r\n      this.handleCheckboxSelection(option);\r\n    } else {\r\n      this.selectedOptions = [option];\r\n      this.selectedValue = option.name;\r\n      // Close dropdown for single select options and reset expanded state\r\n      this.isOpen = false;\r\n      this.expandedOption = null;\r\n      this.emitChanges();\r\n    }\r\n    this.value = this.selectedValue;\r\n    this.onChange(this.selectedValue);\r\n    this.onTouched();\r\n  }\r\n\r\n  handleCheckboxSelection(option: DropdownOption) {\r\n    // Don't handle if option is disabled\r\n    if (option.disabled) {\r\n      return;\r\n    }\r\n\r\n    const index = this.selectedOptions.findIndex(opt => opt.value === option.value);\r\n    if (index > -1) {\r\n      this.selectedOptions.splice(index, 1);\r\n    } else {\r\n      if (this.singleSelect) {\r\n        this.selectedOptions = [option];\r\n      } else {\r\n        this.selectedOptions.push(option);\r\n      }\r\n    }\r\n    this.emitChanges();\r\n  }\r\n\r\n  selectSubOption(subOption: DropdownOption) {\r\n    // Don't select if sub-option is disabled or dropdown is disabled\r\n    if (subOption.disabled || this.disabled) {\r\n      return;\r\n    }\r\n\r\n    // Treat sub-option selection like regular option selection\r\n    this.selectedOptions = [subOption];\r\n    this.selectedValue = subOption.name;\r\n\r\n    // Close dropdown after selecting sub-option\r\n    this.isOpen = false;\r\n    this.expandedOption = null;\r\n    this.emitChanges();\r\n  }\r\n\r\n  toggleSubOptions(optionName: string) {\r\n    // Don't toggle if dropdown is disabled\r\n    if (this.disabled) {\r\n      return;\r\n    }\r\n\r\n    // If clicking the same option, close it; otherwise, open the new one\r\n    this.expandedOption = this.expandedOption === optionName ? null : optionName;\r\n  }\r\n\r\n  isOptionSelected(option: DropdownOption): boolean {\r\n    return this.selectedOptions.some(opt => opt.value === option.value);\r\n  }\r\n\r\n  isSubOptionSelected(subOption: DropdownOption): boolean {\r\n    return this.selectedOptions.some(opt => opt.value === subOption.value);\r\n  }\r\n\r\n  hasSubOptions(optionName: string): boolean {\r\n    return !!(this.suboptions[optionName] && this.suboptions[optionName].length > 0);\r\n  }\r\n\r\n  getOptionIcon(option: DropdownOption): string {\r\n    // If option has a custom icon, use it\r\n    if (option.icon) {\r\n      return option.icon;\r\n    }\r\n    // Otherwise, use the default icon for options in iconOptions array\r\n    return 'circle-check';\r\n  }\r\n\r\n  shouldShowIcon(option: DropdownOption): boolean {\r\n    // Show icon if option has custom icon or if it's in iconOptions array\r\n    return !!(option.icon || this.iconOptions.includes(option.name));\r\n  }\r\n\r\n  isOptionDisabled(option: DropdownOption): boolean {\r\n    return !!(option.disabled || this.disabled);\r\n  }\r\n\r\n  isSubOptionDisabled(subOption: DropdownOption): boolean {\r\n    return !!(subOption.disabled || this.disabled);\r\n  }\r\n\r\n  getDisplayText(): string {\r\n    if (this.selectedOptions.length === 0) {\r\n      return this.dropdownTitle;\r\n    }\r\n    if (this.selectedOptions.length === 1) {\r\n      return this.selectedOptions[0].name;\r\n    }\r\n    return `${this.selectedOptions.length} items selected`;\r\n  }\r\n\r\n  emitChanges() {\r\n    const data = {\r\n      selectedOptions: this.selectedOptions,\r\n      selectedValue: this.selectedValue\r\n    };\r\n    this.selectionChange.emit(data);\r\n    this.valueChange.emit(data);\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event) {\r\n    const target = event.target as HTMLElement;\r\n    const clickedDropdown = target.closest('ava-dropdown');\r\n\r\n    // If clicked outside this dropdown, close it\r\n    if (!this.elementRef.nativeElement.contains(target)) {\r\n      this.closeDropdown();\r\n    }\r\n\r\n    // If a dropdown toggle was clicked, close all other dropdowns\r\n    if (clickedDropdown && target.closest('.dropdown-toggle')) {\r\n      console.log('Dropdown toggle clicked, closing others');\r\n      DropdownComponent.allDropdowns.forEach(dropdown => {\r\n        if (dropdown.elementRef.nativeElement !== clickedDropdown && dropdown.isOpen) {\r\n          console.log('Closing other dropdown via document click');\r\n          dropdown.isOpen = false;\r\n          dropdown.expandedOption = null;\r\n          dropdown.resetFocusStates();\r\n          dropdown.cdr.detectChanges();\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // Close dropdown when clicking on the toggle button while open\r\n  onToggleClick(event: Event) {\r\n    event.stopPropagation();\r\n    this.toggleDropdown();\r\n  }\r\n\r\n  // Keyboard navigation methods\r\n  onToggleKeyDown(event: KeyboardEvent) {\r\n    const actions: Record<string, () => void> = {\r\n      'Enter': () => this.handleToggleActivation(),\r\n      ' ': () => this.handleToggleActivation(),\r\n      'ArrowDown': () => this.openAndFocus('first'),\r\n      'ArrowUp': () => this.openAndFocus('last'),\r\n      'Escape': () => this.isOpen && this.closeDropdown()\r\n    };\r\n\r\n    const action = actions[event.key];\r\n    if (action) {\r\n      event.preventDefault();\r\n      action();\r\n    }\r\n  }\r\n\r\n  onDropdownKeyDown(event: KeyboardEvent) {\r\n    if (!this.isOpen) return;\r\n\r\n    const actions: Record<string, () => void> = {\r\n      'Escape': () => { this.closeDropdown(); this.focusToggle(); },\r\n      'Tab': () => this.closeDropdown()\r\n    };\r\n\r\n    const action = actions[event.key];\r\n    if (action) {\r\n      if (event.key !== 'Tab') event.preventDefault();\r\n      action();\r\n    }\r\n  }\r\n\r\n  onSearchKeyDown(event: KeyboardEvent) {\r\n    const actions: Record<string, () => void> = {\r\n      'ArrowDown': () => {\r\n        if (this.filteredOptions.length > 0) {\r\n          this.focusOption(0);\r\n        }\r\n      },\r\n      'ArrowUp': () => {\r\n        if (this.filteredOptions.length > 0) {\r\n          this.focusOption(this.filteredOptions.length - 1);\r\n        }\r\n      },\r\n      'Escape': () => {\r\n        this.closeDropdown();\r\n        this.focusToggle();\r\n      },\r\n      'Enter': () => {\r\n        if (this.filteredOptions.length > 0) {\r\n          this.selectOption(this.filteredOptions[0]);\r\n        }\r\n      }\r\n    };\r\n\r\n    const action = actions[event.key];\r\n    if (action) {\r\n      event.preventDefault();\r\n      action();\r\n    }\r\n  }\r\n\r\n  onOptionKeyDown(event: KeyboardEvent, option: DropdownOption) {\r\n    const actions: Record<string, () => void> = {\r\n      'Enter': () => this.handleOptionActivation(option),\r\n      ' ': () => this.handleOptionActivation(option),\r\n      'ArrowDown': () => this.navigateOptions(1),\r\n      'ArrowUp': () => this.navigateOptions(-1),\r\n      'ArrowRight': () => this.expandSubOptions(option),\r\n      'Escape': () => { this.closeDropdown(); this.focusToggle(); },\r\n      'Home': () => this.focusOption(0),\r\n      'End': () => this.focusOption(this.filteredOptions.length - 1)\r\n    };\r\n\r\n    const action = actions[event.key];\r\n    if (action) {\r\n      event.preventDefault();\r\n      action();\r\n    }\r\n  }\r\n\r\n  onSubOptionKeyDown(event: KeyboardEvent, subOption: DropdownOption) {\r\n    const actions: Record<string, () => void> = {\r\n      'Enter': () => this.selectSubOption(subOption),\r\n      ' ': () => this.selectSubOption(subOption),\r\n      'ArrowDown': () => this.navigateSubOptions(1),\r\n      'ArrowUp': () => this.navigateSubOptions(-1),\r\n      'ArrowLeft': () => { this.expandedOption = null; this.focusOption(this.focusedOptionIndex); },\r\n      'Escape': () => { this.closeDropdown(); this.focusToggle(); }\r\n    };\r\n\r\n    const action = actions[event.key];\r\n    if (action) {\r\n      event.preventDefault();\r\n      action();\r\n    }\r\n  }\r\n\r\n  // Optimized helper methods\r\n  private handleToggleActivation() {\r\n    this.toggleDropdown();\r\n    if (this.isOpen) {\r\n      if (this.search || this.enableSearch) {\r\n        this.focusSearchInput();\r\n      } else {\r\n        this.focusOption(0);\r\n      }\r\n    }\r\n  }\r\n\r\n  private openAndFocus(position: 'first' | 'last') {\r\n    if (!this.isOpen) this.toggleDropdown();\r\n\r\n    if (this.search || this.enableSearch) {\r\n      this.focusSearchInput();\r\n    } else {\r\n      this.focusOption(position === 'first' ? 0 : this.filteredOptions.length - 1);\r\n    }\r\n  }\r\n\r\n  private handleOptionActivation(option: DropdownOption) {\r\n    if (this.hasSubOptions(option.name)) {\r\n      this.toggleSubOptions(option.name);\r\n      if (this.expandedOption === option.name) this.focusSubOption(0);\r\n    } else {\r\n      this.selectOption(option);\r\n    }\r\n  }\r\n\r\n  private expandSubOptions(option: DropdownOption) {\r\n    if (this.hasSubOptions(option.name)) {\r\n      this.expandedOption = option.name;\r\n      this.focusSubOption(0);\r\n    }\r\n  }\r\n\r\n  private navigateOptions(direction: number) {\r\n    const newIndex = this.focusedOptionIndex + direction;\r\n\r\n    // If going up from first option and search is enabled, focus search\r\n    if (direction === -1 && this.focusedOptionIndex === 0 && (this.search || this.enableSearch)) {\r\n      this.focusSearchInput();\r\n      this.resetFocusStates();\r\n      return;\r\n    }\r\n\r\n    if (newIndex >= 0 && newIndex < this.filteredOptions.length) {\r\n      this.focusOption(newIndex);\r\n    }\r\n  }\r\n\r\n  private navigateSubOptions(direction: number) {\r\n    const currentOption = this.filteredOptions[this.focusedOptionIndex];\r\n    const subOptions = this.suboptions[currentOption.name];\r\n    if (!subOptions) return;\r\n\r\n    const newIndex = this.focusedSubOptionIndex + direction;\r\n    if (newIndex >= 0 && newIndex < subOptions.length) {\r\n      this.focusSubOption(newIndex);\r\n    }\r\n  }\r\n\r\n  private focusOption(index: number) {\r\n    this.focusedOptionIndex = index;\r\n    this.isNavigatingSubOptions = false;\r\n    this.focusedSubOptionIndex = -1;\r\n    setTimeout(() => this.focusElement('.option', index), 0);\r\n  }\r\n\r\n  private focusSubOption(index: number) {\r\n    this.focusedSubOptionIndex = index;\r\n    this.isNavigatingSubOptions = true;\r\n    setTimeout(() => this.focusElement('.suboption', index), 0);\r\n  }\r\n\r\n  private focusElement(selector: string, index: number) {\r\n    const elements = this.elementRef.nativeElement.querySelectorAll(selector);\r\n    if (elements[index]) {\r\n      (elements[index] as HTMLElement).focus();\r\n    }\r\n  }\r\n\r\n  private focusSearchInput() {\r\n    setTimeout(() => {\r\n      const searchInput = this.elementRef.nativeElement.querySelector('.search-box input');\r\n      if (searchInput) (searchInput as HTMLElement).focus();\r\n    }, 0);\r\n  }\r\n\r\n  private focusToggle() {\r\n    const toggle = this.elementRef.nativeElement.querySelector('.dropdown-toggle');\r\n    if (toggle) (toggle as HTMLElement).focus();\r\n  }\r\n\r\n  // Calculate position for sub-options based on option index\r\n  getSuboptionPosition(optionIndex: number): number {\r\n    // Calculate position relative to the dropdown \r\n    const toggleHeight = 50;\r\n    const dropdownMenuTop = 2;\r\n    const searchBoxHeight = (this.search || this.enableSearch) ? 40 : 0; \r\n    const optionHeight = 40; // Height of each option\r\n    const optionsContainerPadding = 0; // No padding on options container\r\n\r\n    // Position to align the first sub-option with the hovered main option\r\n    return toggleHeight + dropdownMenuTop + searchBoxHeight + optionsContainerPadding + (optionIndex * optionHeight);\r\n  }\r\n}\r\n", "<div class=\"ava-dropdown\" [class.open]=\"isOpen\" (keydown)=\"onDropdownKeyDown($event)\">\r\n  <!-- Dropdown Toggle -->\r\n  <button class=\"dropdown-toggle\"\r\n          [class.open]=\"isOpen\"\r\n          [class.disabled]=\"disabled\"\r\n          [disabled]=\"disabled\"\r\n          (click)=\"onToggleClick($event)\"\r\n          (keydown)=\"onToggleKeyDown($event)\"\r\n          [attr.aria-expanded]=\"isOpen\"\r\n          [attr.aria-haspopup]=\"'listbox'\"\r\n          [attr.aria-label]=\"getDisplayText()\">\r\n    <span>{{ getDisplayText() }}</span>\r\n    <ava-icon [iconName]=\"dropdownIcon\" [iconSize]=\"16\" [style.transform]=\"isOpen ? 'rotate(180deg)' : 'rotate(0deg)'\"></ava-icon>\r\n  </button>\r\n\r\n  <!-- Dropdown Menu -->\r\n  <div class=\"dropdown-menu\"\r\n       *ngIf=\"isOpen\"\r\n       role=\"listbox\"\r\n       [attr.aria-label]=\"dropdownTitle\">\r\n\r\n    <!-- Search -->\r\n    <div class=\"search-box\" *ngIf=\"search || enableSearch\">\r\n      <input\r\n        type=\"text\"\r\n        placeholder=\"Search...\"\r\n        [(ngModel)]=\"searchTerm\"\r\n        (input)=\"onSearch()\"\r\n        (keydown)=\"onSearchKeyDown($event)\"\r\n        [attr.aria-label]=\"'Search ' + dropdownTitle\"\r\n        tabindex=\"0\"\r\n        autocomplete=\"off\">\r\n      <ava-icon [iconName]=\"'search'\" [iconSize]=\"14\"></ava-icon>\r\n    </div>\r\n\r\n    <!-- Options -->\r\n    <div class=\"options\" role=\"group\">\r\n      <div *ngFor=\"let option of filteredOptions; let i = index\" class=\"option-group\">\r\n\r\n        <!-- Main Option -->\r\n        <div class=\"option\"\r\n             [class.selected]=\"isOptionSelected(option)\"\r\n             [class.has-suboptions]=\"hasSubOptions(option.name)\"\r\n             [class.checkbox-option]=\"checkboxOptions.includes(option.name)\"\r\n             [class.focused]=\"focusedOptionIndex === i\"\r\n             [class.disabled]=\"isOptionDisabled(option)\"\r\n             (click)=\"hasSubOptions(option.name) ? toggleSubOptions(option.name) : selectOption(option); $event.stopPropagation()\"\r\n             (keydown)=\"onOptionKeyDown($event, option)\"\r\n             [attr.tabindex]=\"focusedOptionIndex === i ? '0' : '-1'\"\r\n             [attr.role]=\"'option'\"\r\n             [attr.aria-selected]=\"isOptionSelected(option)\"\r\n             [attr.aria-expanded]=\"hasSubOptions(option.name) ? (expandedOption === option.name) : null\"\r\n             [attr.aria-label]=\"option.name\"\r\n             [attr.aria-disabled]=\"isOptionDisabled(option)\">\r\n\r\n          <!-- With Checkbox -->\r\n          <ava-checkbox\r\n            *ngIf=\"checkboxOptions.includes(option.name)\"\r\n            variant=\"with-bg\"\r\n            [isChecked]=\"isOptionSelected(option)\"\r\n            [label]=\"option.name\"\r\n            [disable]=\"isOptionDisabled(option)\">\r\n          </ava-checkbox>\r\n\r\n          <!-- Without Checkbox -->\r\n          <div *ngIf=\"!checkboxOptions.includes(option.name)\" class=\"option-content\">\r\n            <ava-icon\r\n              *ngIf=\"shouldShowIcon(option)\"\r\n              [iconName]=\"getOptionIcon(option)\"\r\n              [iconSize]=\"16\">\r\n            </ava-icon>\r\n            <span>{{ option.name }}</span>\r\n          </div>\r\n\r\n          <!-- Sub-options arrow: down by default, right when expanded -->\r\n          <ava-icon\r\n            *ngIf=\"hasSubOptions(option.name)\"\r\n            [iconName]=\"expandedOption === option.name ? 'chevron-right' : 'chevron-down'\"\r\n            [iconSize]=\"12\"\r\n            (click)=\"toggleSubOptions(option.name); $event.stopPropagation()\">\r\n          </ava-icon>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- No results -->\r\n    <div *ngIf=\"filteredOptions.length === 0\" class=\"no-results\">\r\n      No options found\r\n    </div>\r\n  </div>\r\n\r\n    <!-- Sub-options positioned next to hovered option -->\r\n    <ng-container *ngFor=\"let option of filteredOptions; let optionIndex = index\">\r\n      <div class=\"suboptions-overlay\"\r\n           *ngIf=\"expandedOption === option.name && isOpen\"\r\n           [style.top.px]=\"getSuboptionPosition(optionIndex)\"\r\n           role=\"listbox\"\r\n           [attr.aria-label]=\"'Sub-options for ' + option.name\">\r\n        <div class=\"suboptions-panel\">\r\n          <div *ngFor=\"let subOption of suboptions[option.name]; let i = index\"\r\n               class=\"suboption\"\r\n               [class.selected]=\"isSubOptionSelected(subOption)\"\r\n               [class.focused]=\"focusedSubOptionIndex === i\"\r\n               [class.disabled]=\"isSubOptionDisabled(subOption)\"\r\n               (click)=\"selectSubOption(subOption); $event.stopPropagation()\"\r\n               (keydown)=\"onSubOptionKeyDown($event, subOption)\"\r\n               [attr.tabindex]=\"focusedSubOptionIndex === i ? '0' : '-1'\"\r\n               [attr.role]=\"'option'\"\r\n               [attr.aria-selected]=\"isSubOptionSelected(subOption)\"\r\n               [attr.aria-label]=\"subOption.name\"\r\n               [attr.aria-disabled]=\"isSubOptionDisabled(subOption)\">\r\n            <span>{{ subOption.name }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ng-container>\r\n  </div>\r\n", "import { CommonModule } from '@angular/common';\r\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, ViewEncapsulation } from '@angular/core';\r\nimport { ButtonComponent } from '../button/button.component';\r\n\r\n@Component({\r\n  selector: 'ava-sidebar',\r\n  imports: [CommonModule, ButtonComponent],\r\n  templateUrl: './sidebar.component.html',\r\n  styleUrl: './sidebar.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class SidebarComponent implements OnInit, OnChanges {\r\n  @Input() width: string = '260px';\r\n  @Input() collapsedWidth: string = '50px';\r\n  @Input() height: string = '100vh';\r\n  @Input() hoverAreaWidth: string = '10px';\r\n  @Input() showCollapseButton: boolean = false;\r\n  @Input() buttonVariant: 'inside' | 'outside' = 'inside';\r\n  @Input() showHeader: boolean = true;\r\n  @Input() showFooter: boolean = true;\r\n  @Input() isCollapsed: boolean = false;\r\n\r\n  @Output() collapseToggle = new EventEmitter<boolean>();\r\n\r\n  private _isCollapsed = false;\r\n\r\n  constructor(private cdr: ChangeDetectorRef) { }\r\n\r\n  ngOnInit() {\r\n    this._isCollapsed = this.isCollapsed;\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['isCollapsed'] && !changes['isCollapsed'].firstChange) {\r\n      this._isCollapsed = this.isCollapsed;\r\n      this.cdr.markForCheck();\r\n    }\r\n  }\r\n\r\n  toggleCollapse(): void {\r\n    this._isCollapsed = !this._isCollapsed;\r\n    this.collapseToggle.emit(this._isCollapsed);\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  get sidebarWidth(): string {\r\n    return this._isCollapsed ? this.collapsedWidth : this.width;\r\n  }\r\n\r\n  get collapsed(): boolean {\r\n    return this._isCollapsed;\r\n  }\r\n}", "<div class=\"ava-sidebar-container\">\r\n  <!-- Main Sidebar -->\r\n\r\n  <div\r\n    class=\"ava-sidebar\"\r\n    [style.width]=\"sidebarWidth\"\r\n    [style.height]=\"height\"\r\n    [class.collapsed]=\"collapsed\"\r\n  >\r\n    <!-- Header Section -->\r\n\r\n    <div class=\"sidebar-header\" *ngIf=\"showHeader\">\r\n      <div class=\"header-content\" *ngIf=\"!collapsed\">\r\n        <ng-content select=\"[slot=header]\"></ng-content>\r\n      </div>\r\n\r\n      <!-- Inside Button Variant -->\r\n\r\n      <div\r\n        class=\"header-controls\"\r\n        *ngIf=\"showCollapseButton && buttonVariant === 'inside'\"\r\n      >\r\n        <ava-button\r\n          class=\"collapse-btn\"\r\n          (click)=\"toggleCollapse()\"\r\n          iconPosition=\"only\"\r\n          [iconName]=\"collapsed ? 'ArrowRight' : 'ArrowLeft'\"\r\n        >\r\n        </ava-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Main Content Section -->\r\n\r\n    <div class=\"sidebar-content\">\r\n      <ng-content select=\"[slot=content]\"></ng-content>\r\n    </div>\r\n\r\n    <!-- Footer Section -->\r\n\r\n    <div class=\"sidebar-footer\" *ngIf=\"showFooter\">\r\n      <ng-content select=\"[slot=footer]\"></ng-content>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Outside Button Variant - Hover Area -->\r\n\r\n  <div\r\n    class=\"hover-area\"\r\n    [style.width]=\"hoverAreaWidth\"\r\n    [style.height]=\"height\"\r\n    *ngIf=\"showCollapseButton && buttonVariant === 'outside'\"\r\n  >\r\n    <div class=\"hover-area-content\">\r\n      <ava-button\r\n        class=\"collapse-btn\"\r\n        (click)=\"toggleCollapse()\"\r\n        iconPosition=\"only\"\r\n        [iconName]=\"collapsed ? 'ArrowRight' : 'ArrowLeft'\"\r\n      >\r\n      </ava-button>\r\n    </div>\r\n  </div>\r\n</div>\r\n", "import { CommonModule } from '@angular/common';\r\nimport { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, HostListener, Input, Output, ViewChild, WritableSignal, forwardRef, signal } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'ava-slider',\r\n  imports: [CommonModule],\r\n  templateUrl: './slider.component.html',\r\n  styleUrl: './slider.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => SliderComponent),\r\n      multi: true\r\n    }\r\n  ]\r\n})\r\nexport class SliderComponent implements ControlValueAccessor {\r\n  @Input() min = 0;\r\n  @Input() max = 100;\r\n  @Input() value = 0;\r\n  @Input() step = 1;\r\n  @Input() showTooltip = true;\r\n\r\n  @Output() valueChange = new EventEmitter<number>();\r\n  @ViewChild('sliderTrack') sliderTrack!: ElementRef;\r\n\r\n  isHovered = false;\r\n  isDragging = false;\r\n\r\n  private onChange: (value: number) => void = () => {};\r\n  private onTouched: () => void = () => {};\r\n\r\n  decimalStepValue: WritableSignal<number> = signal(1);\r\n\r\n  constructor(private elementRef: ElementRef) {}\r\n\r\n  get percentage(): number {\r\n    return ((this.value - this.min) / (this.max - this.min)) * 100;\r\n  }\r\n\r\n  // ControlValueAccessor methods\r\n  writeValue(value: number): void {\r\n    this.value = value || 0;\r\n    // Optional: update internal state/UI if needed\r\n  }\r\n\r\n  registerOnChange(fn: any): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: any): void {\r\n    this.onTouched = fn;\r\n  }\r\n\r\n  onTrackClick(event: MouseEvent): void {\r\n    this.updateValueFromEvent(event);\r\n  }\r\n\r\n  startDrag(event: MouseEvent): void {\r\n    event.preventDefault();\r\n    this.isDragging = true;\r\n    this.isHovered = true;\r\n  }\r\n\r\n  onKeyDown(event: KeyboardEvent): void {\r\n    let newValue = this.value;\r\n    switch (event.key) {\r\n      case 'ArrowRight':\r\n      case 'ArrowUp':\r\n        newValue = this.value + this.step;\r\n        break;\r\n      case 'ArrowLeft':\r\n      case 'ArrowDown':\r\n        newValue = this.value - this.step;\r\n        break;\r\n      case 'Home':\r\n        newValue = this.min;\r\n        break;\r\n      case 'End':\r\n        newValue = this.max;\r\n        break;\r\n      default:\r\n        return;\r\n    }\r\n    event.preventDefault();\r\n    this.updateValue(newValue);\r\n  }\r\n\r\n  @HostListener('document:mousemove', ['$event'])\r\n  onMouseMove(event: MouseEvent): void {\r\n    if (this.isDragging) {\r\n      this.updateValueFromEvent(event);\r\n    }\r\n  }\r\n\r\n  @HostListener('document:mouseup')\r\n  onMouseUp(): void {\r\n    if (this.isDragging) {\r\n      this.isDragging = false;\r\n      this.isHovered = false;\r\n      this.onTouched();\r\n    }\r\n  }\r\n\r\n  onDecimalStepChange(value: number): void {\r\n  this.decimalStepValue.set(parseFloat(value.toFixed(2)));\r\n}\r\n\r\n  private updateValueFromEvent(event: MouseEvent): void {\r\n    const rect = this.sliderTrack.nativeElement.getBoundingClientRect();\r\n    const percentage = (event.clientX - rect.left) / rect.width;\r\n    const newValue = this.min + percentage * (this.max - this.min);\r\n    this.updateValue(newValue);\r\n  }\r\n\r\n  private updateValue(value: number): void {\r\n    const roundedValue = Math.round(value / this.step) * this.step;\r\n    const clampedValue = Math.max(this.min, Math.min(this.max, roundedValue));\r\n    if (clampedValue !== this.value) {\r\n      this.value = clampedValue;\r\n      this.onChange(this.value);\r\n      this.valueChange.emit(this.value);\r\n    }\r\n  }\r\n}\r\n", "<div class=\"slider-container\">\r\n  <div class=\"slider\" #sliderTrack (mousedown)=\"onTrackClick($event)\">\r\n    <div class=\"slider-fill\" [style.width.%]=\"percentage\"></div>\r\n    <div class=\"slider-handle\" [style.left.%]=\"percentage\" [class.hover]=\"isHovered\" [class.dragging]=\"isDragging\"\r\n      tabindex=\"0\" role=\"slider\" [attr.aria-valuemin]=\"min\" [attr.aria-valuemax]=\"max\"\r\n      [attr.aria-valuenow]=\"value\" (mousedown)=\"startDrag($event)\" (mouseenter)=\"isHovered = true\"\r\n      (mouseleave)=\"isHovered = false\" (keydown)=\"onKeyDown($event)\">\r\n      <div class=\"handle-ring\"></div>\r\n      <div class=\"handle-core\"></div>\r\n      <!-- Movable Tooltip -->\r\n      <div class=\"slider-tooltip\" *ngIf=\"showTooltip\">\r\n        {{value}}%\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>", "import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { PopupComponent } from '../../components/popup/popup.component';\r\nimport { CommonModule } from '@angular/common';\r\nimport { AvaTextareaComponent } from '../../components/textarea/ava-textarea.component';\r\n\r\n@Component({\r\n  selector: 'ava-confirmation-popup',\r\n  standalone: true,\r\n  imports: [CommonModule, PopupComponent, AvaTextareaComponent],\r\n  templateUrl: './confirmation-popup.component.html',\r\n  styleUrl: './confirmation-popup.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class ConfirmationPopupComponent {\r\n\r\n  // Input: Confirm button label text (default: 'Yes')\r\n  @Input() confirmationLabel = 'Yes';\r\n\r\n  // Input: Title text of the popup\r\n  @Input() title = 'title';\r\n\r\n  // Input: Message/body text of the popup\r\n  @Input() message = 'message';\r\n\r\n  // Input: Controls visibility of the popup\r\n  @Input() show = false;\r\n\r\n  // Output: Emits when the popup is closed (via close button or confirm)\r\n  @Output() closed = new EventEmitter<void>();\r\n\r\n\r\n\r\n  // Called when confirm button is clicked\r\n  handleConfirm(text: string): void {\r\n    console.log(text);\r\n    this.closed.emit(); // Emit closed event\r\n  }\r\n\r\n  // Called when cancel button is clicked\r\n  handleCancel(): void {\r\n    console.log('Cancel clicked');\r\n    this.show = false; // Hide the popup\r\n  }\r\n\r\n  // Called when close icon is clicked\r\n  handleClose(): void {\r\n    this.show = false; // Hide the popup\r\n  }\r\n}\r\n", "<div class=\"ava-confirmation-popup-container\">\r\n    <ava-popup [show]=\"show\" [popupWidth]=\"'500px'\" [title]=\"title\" [message]=\"message\" [showHeaderIcon]=\"false\"\r\n        [showClose]=\"true\" [showConfirm]=\"true\" [showCancel]=\"true\" [confirmButtonLabel]=\"confirmationLabel\"\r\n        (closed)=\"handleClose()\" (confirm)=\"handleConfirm(feedbackText.value)\" (cancel)=\"handleCancel()\" role=\"dialog\"\r\n        aria-modal=\"true\" aria-labelledby=\"popup-title\" aria-describedby=\"popup-message\">\r\n\r\n        <form class=\"popup-form\">\r\n            <ava-textarea label=\"Your Feedback\" placeholder=\"Type here...\" name=\"feedback\" #feedbackText>\r\n            </ava-textarea>\r\n        </form>\r\n\r\n    </ava-popup>\r\n</div>", "export const APP_CONSTANTS = {\r\n  UPLOAD_TITLE: 'Upload File Here',\r\n  CLOSE_UPLOAD_DIALOG: 'Close upload dialog',\r\n  SUPPORTED_FORMATS_LABEL: 'Supported file formats',\r\n  SUPPORTED_FORMATS_LIST: 'JPEG, PNG, SVG, DOC, DOCX, XLSX, TXT, PDF',\r\n  CLICK_TO_UPLOAD: 'Click to upload a file',\r\n  FILE_UPLOADED_SUCCESS: 'File successfully uploaded',\r\n  CLICK_TO_REUPLOAD: 'Click here to re-upload',\r\n  INVALID_FILE_TYPE: 'Invalid file type. Allowed formats: JPEG, PNG, SVG, DOC, DOCX, XLSX, TXT, PDF.',\r\n  FILE_SIZE_ERROR: 'File is too large. Maximum size allowed is 3MB.',\r\n  UPLOAD_BUTTON: 'Upload',\r\n  FILE_UPLOAD_PROMPT: 'Click here to upload a file',\r\n  MAX_FILE_SIZE : 3 * 1024 * 1024,\r\n  ALLOWED_EXTENSIONS: ['jpeg', 'jpg', 'png', 'svg', 'doc', 'docx', 'xlsx', 'txt', 'pdf'], \r\n  ACCEPTED_FILE_TYPES: '.jpeg,.jpg,.png,.svg,.doc,.docx,.xlsx,.txt,.pdf', \r\n  MAX_FILES_ERROR: 'Maximum of 5 files allowed',\r\n\r\n\r\n  //authentication flow components constants\r\n\r\n\r\n  PHONE_REQUIRED_ERROR: 'Phone number is required.',\r\n  PHONE_FORMAT_ERROR: 'Phone number must be exactly 13 digits starting with +91.',\r\n  AUTHENTICATION_TITLE: 'Authentication',\r\n  SENT_TITLE: 'Sent!',\r\n  VERIFICATION_SUCCESS_TITLE: 'Verification Successful',\r\n  NEED_TO_VERIFY: 'Need to verify user to authenticate',\r\n  CANCEL_BUTTON: 'Cancel',\r\n  PROCEED_TO_VERIFY_BUTTON: 'Proceed to Verify',\r\n  USERNAME_PLACEHOLDER: 'Enter username',\r\n  USERNAME_REQUIRED_ERROR: 'Username is required and must be at least 3 characters.',\r\n  ENTER_EMAIL: 'Enter email',\r\n  SEND_CODE_EMAIL: 'Send code via email',\r\n  EMAIL_REQUIRED_ERROR: 'Please enter a valid email address.',\r\n  ENTER_PHONE: 'Enter phone number',\r\n  SEND_CODE_SMS: 'Send code via SMS',\r\n  VERIFICATION_SENT_EMAIL: 'A verification mail has been successfully sent to the contact email.',\r\n  VERIFICATION_SENT_SMS: 'A verification code has been successfully sent to the contact phone number.',\r\n  CLOSE_BUTTON: 'Close',\r\n  VERIFICATION_CODE_PLACEHOLDER: 'Enter verification code',\r\n  VERIFICATION_CODE_ERROR: 'Verification code must be 6 digits.',\r\n  PASSWORD_PLACEHOLDER: 'Enter the password',\r\n  PASSWORD_ERROR: 'Password must be at least 6 characters long.',\r\n  VERIFICATION_SUCCESS: 'Verification done Successfully',\r\n\r\n\r\n  \r\n};", "import { CommonModule } from '@angular/common';\r\nimport { ChangeDetectionStrategy, Component, EventEmitter, Output, HostListener, Input, OnInit } from '@angular/core';\r\nimport { IconComponent } from '../icon/icon.component';\r\nimport { APP_CONSTANTS } from '../../constants/app.constants';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport { ButtonComponent } from '../button/button.component';\r\nimport { AvaTagComponent } from '../tags/tags.component';\r\n\r\n@Component({\r\n  selector: 'ava-file-upload',\r\n  standalone: true,\r\n  imports: [CommonModule, IconComponent, LucideAngularModule, ButtonComponent, AvaTagComponent],\r\n  templateUrl: './fileupload.component.html',\r\n  styleUrls: ['./fileupload.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n\r\n})\r\nexport class FileUploadComponent implements OnInit {\r\n  @Output() public fileUploaded = new EventEmitter<File>();\r\n  @Output() public filesListChanged = new EventEmitter<File[]>();\r\n  @Input() theme: 'light' | 'dark' = 'light';\r\n  @Input() uploaderId: string = '';\r\n  @Input() enableAnimation: boolean = false;\r\n  @Input() allowedFormats: string[] = [];\r\n  @Input() singleFileMode: boolean = false;\r\n  @Input() maxFiles: number | null = null;\r\n  @Input() componentTitle:string = \"Upload File Here\";\r\n  @Input() supportedFormatLabel = \"Supported file formats\";\r\n  @Input() maxFileSize = 3 * 1024 * 1024; //3MB default\r\n\r\n  appConstants = APP_CONSTANTS;\r\n  public uploadedFiles: File[] = [];\r\n  public fileUploadedSuccess = false;\r\n  public fileFormatError = false;\r\n  public fileSizeError = false;\r\n  public maxFilesError = false;\r\n  public isUploadActive = false;\r\n  public viewAll: boolean = false;\r\n\r\n  // Public so it's accessible from the template\r\n  public uniqueId: string = '';\r\n\r\n  ngOnInit(): void {\r\n    // Generate a unique ID if not provided\r\n    this.uniqueId = this.uploaderId || 'uploader-' + Math.random().toString(36).substring(2, 9);\r\n  }\r\n\r\n  \r\n\r\n  get allowedFormatsList(): string[] {\r\n    return this.allowedFormats.length > 0 ? this.allowedFormats : ['jpeg', 'jpg', 'png', 'svg', 'doc', 'docx', 'xlsx', 'txt', 'pdf']\r\n  }\r\n  toggleViewAll() {\r\n    this.viewAll = !this.viewAll;\r\n  }\r\n   sizeFormat(bytes: number, decimals: number = 2): string {\r\n    if (isNaN(bytes) || bytes === 0) return '0 Bytes';\r\n\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    const value = bytes / Math.pow(k, i);\r\n\r\n    return `${parseFloat(value.toFixed(decimals))} ${sizes[i]}`;\r\n  }\r\n  public onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      this.handleFile(input.files[0]);\r\n\r\n      // Reset the input value to allow selecting the same file again if needed\r\n      input.value = '';\r\n    }\r\n  }\r\n\r\n  // HostListener to handle dragover event\r\n  @HostListener('dragover', ['$event'])\r\n  public onDragOver(event: DragEvent): void {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n  }\r\n\r\n  // HostListener to handle drop event\r\n  @HostListener('drop', ['$event'])\r\n  public onDrop(event: DragEvent): void {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    const files = event.dataTransfer?.files;\r\n    if (files && files.length > 0) {\r\n      this.handleFile(files[0]);\r\n    }\r\n  }\r\n\r\n  // Centralized method to handle file validation and selection\r\n  private handleFile(file: File): void {\r\n    const fileExtension = file.name.split('.').pop()?.toLowerCase();\r\n    // console.log(\"allowedFormats:\", this.allowedFormats);\r\n    if (!fileExtension || !this.allowedFormatsList.includes(fileExtension)) {\r\n      this.fileFormatError = true;\r\n      this.fileSizeError = false;\r\n      this.maxFilesError = false;\r\n      return;\r\n    }\r\n\r\n    if (file.size > this.maxFileSize) {\r\n      this.fileSizeError = true;\r\n      this.fileFormatError = false;\r\n      this.maxFilesError = false;\r\n      return;\r\n    }\r\n\r\n    // Check for max files limitation\r\n    if (this.maxFiles !== null && !this.singleFileMode && this.uploadedFiles.length >= this.maxFiles) {\r\n      this.maxFilesError = true;\r\n      this.fileFormatError = false;\r\n      this.fileSizeError = false;\r\n      return;\r\n    }\r\n\r\n    // If in single file mode, replace the existing file\r\n    if (this.singleFileMode) {\r\n      this.uploadedFiles = [file];\r\n    } else {\r\n      // Add the file to the list of uploaded files\r\n      const isDuplicate = this.uploadedFiles.some(existingFile =>\r\n        existingFile.name === file.name && existingFile.size === file.size);\r\n\r\n      if (!isDuplicate) {\r\n        this.uploadedFiles.push(file);\r\n      }\r\n    }\r\n\r\n    this.filesListChanged.emit(this.uploadedFiles);\r\n    this.fileFormatError = false;\r\n    this.fileSizeError = false;\r\n    this.maxFilesError = false;\r\n    this.isUploadActive = true;\r\n  }\r\n\r\n  public openFileSelector(): void {\r\n    // Check for max files before opening selector\r\n    if (this.maxFiles !== null && !this.singleFileMode && this.uploadedFiles.length >= this.maxFiles) {\r\n      this.maxFilesError = true;\r\n      return;\r\n    }\r\n\r\n    const fileInput = document.getElementById('fileInput-' + this.uniqueId) as HTMLInputElement;\r\n    if (fileInput) {\r\n      fileInput.click();\r\n    }\r\n  }\r\n\r\n  public uploadFile(): void {\r\n    if (this.uploadedFiles.length > 0) {\r\n      // Emit each file individually to maintain backward compatibility\r\n      this.uploadedFiles.forEach(file => {\r\n        this.fileUploaded.emit(file);\r\n      });\r\n      this.fileUploadedSuccess = true;\r\n    } else {\r\n      // If no files are selected, open the file dialog\r\n      this.openFileSelector();\r\n    }\r\n  }\r\n\r\n  public removeFile(index: number): void {\r\n    if (index >= 0 && index < this.uploadedFiles.length) {\r\n      this.uploadedFiles.splice(index, 1);\r\n      this.filesListChanged.emit(this.uploadedFiles);\r\n\r\n      // Reset success state if all files are removed\r\n      if (this.uploadedFiles.length === 0) {\r\n        this.fileUploadedSuccess = false;\r\n      }\r\n\r\n      // Reset max files error when a file is removed\r\n      this.maxFilesError = false;\r\n    }\r\n  }\r\n\r\n  public resetUpload(): void {\r\n    this.uploadedFiles = [];\r\n    this.fileUploadedSuccess = false;\r\n    this.fileFormatError = false;\r\n    this.fileSizeError = false;\r\n    this.maxFilesError = false;\r\n    this.filesListChanged.emit(this.uploadedFiles);\r\n  }\r\n\r\n  public closeUpload(): void {\r\n    this.resetUpload();\r\n  }\r\n}\r\n", "<div class=\"upload-container\" role=\"dialog\" [ngClass]=\"[theme, enableAnimation ? 'animated' : '']\"\r\n    [attr.aria-labelledby]=\"'upload-title-' + uniqueId\" [attr.aria-describedby]=\"'upload-desc-' + uniqueId\"\r\n    (dragover)=\"onDragOver($event)\" (drop)=\"onDrop($event)\">\r\n    <div class=\"file-upload-header\">\r\n        <div class=\"file-upload-header-title\" [id]=\"'upload-title-' + uniqueId\">{{componentTitle}}</div>\r\n        <button class=\"close-button\" (click)=\"closeUpload()\" [attr.aria-label]=\"'Close upload dialog'\">\r\n            <ava-icon iconName=\"X\" [iconSize]=\"24\" iconColor=\"#a1a1a1\"></ava-icon>\r\n        </button>\r\n    </div>\r\n\r\n    <p class=\"supported-file\" [id]=\"'upload-desc-' + uniqueId\">{{supportedFormatLabel}}</p>\r\n    <p class=\"file-formats\">{{allowedFormatsList.join(', ') | uppercase}}</p>\r\n\r\n    <label class=\"upload-area\" [attr.for]=\"'fileInput-' + uniqueId\" [attr.aria-label]=\"'Click here to upload a file'\">\r\n        <input [id]=\"'fileInput-' + uniqueId\" type=\"file\" (change)=\"onFileSelected($event)\"\r\n            [attr.accept]=\"allowedFormatsList.join(',')\" [attr.aria-describedby]=\"'file-input-desc-' + uniqueId\"\r\n            hidden />\r\n        <div class=\"upload-placeholder\">\r\n            <ava-icon iconName=\"image\" aria-hidden=\"true\" [iconSize]=\"24\" [iconColor]=\"'#a1a1a1'\"></ava-icon>\r\n            <div class=\"\" *ngIf=\"uploadedFiles.length > 0; else dragToUpload\">\r\n                <p class=\"click-here\" [id]=\"'file-input-desc-' + uniqueId\">\r\n                    Your file was added successfully\r\n                </p>\r\n                <p class=\"click-here active\" [id]=\"'file-input-desc-' + uniqueId\">\r\n                    {{singleFileMode && uploadedFiles.length > 0 ? 'Click to replace file' :\r\n                  'Click here to add more' }}\r\n                </p>\r\n            </div>\r\n            <ng-template #dragToUpload>\r\n                <p class=\"click-here \" [id]=\"'file-input-desc-' + uniqueId\">\r\n                    Drag and Drop your file here\r\n                </p>\r\n\r\n                <p class=\"click-here\" [id]=\"'file-input-desc-' + uniqueId\">\r\n                    {{singleFileMode && uploadedFiles.length > 0 ? 'Click to replace file' :\r\n                    'Click here to upload'}}\r\n                </p>\r\n            </ng-template>\r\n        </div>\r\n    </label>\r\n\r\n    @if (fileFormatError) {\r\n    <div class=\"error-message\" role=\"alert\">\r\n        Invalid file type. Allowed formats:{{allowedFormatsList.join(', ') | uppercase}}.\r\n    </div>\r\n    }\r\n\r\n    @if (fileSizeError) {\r\n    <div class=\"error-message\" role=\"alert\">\r\n        File is too large. Maximum size allowed is {{ sizeFormat(maxFileSize)}}\r\n    </div>\r\n    }\r\n\r\n    @if (maxFilesError) {\r\n    <div class=\"error-message\" role=\"alert\">\r\n        Maximum of {{maxFiles}} files allowed\r\n    </div>\r\n    }\r\n\r\n\r\n\r\n    <div class=\"file-actions\">\r\n\r\n        <div class=\"files-list\" *ngIf=\"uploadedFiles.length > 0\">\r\n            <ng-container *ngIf=\"!viewAll; else viewAllFiles\">\r\n                <div *ngFor=\"let file of uploadedFiles.slice(0,2); let i = index\">\r\n                    <ava-tag [label]=\"file.name | lowercase\" [color]=\"'success'\"\r\n                        [iconColor]=\"theme=='dark'?'#a1a1aa':'#059669'\" [variant]=\"'outlined'\" [size]=\"'sm'\"\r\n                        [removable]=\"true\" (removed)=\"removeFile(i)\"></ava-tag>\r\n                </div>\r\n                <span *ngIf=\"uploadedFiles.length > 2\">\r\n                    ...+{{ uploadedFiles.length - 2 }} more\r\n                </span>\r\n\r\n            </ng-container>\r\n            <ng-template #viewAllFiles>\r\n                <div *ngFor=\"let file of uploadedFiles; let i = index\">\r\n                    <ava-tag [label]=\"file.name | lowercase\" [color]=\"'success'\"\r\n                        [iconColor]=\"theme=='dark'?'#a1a1aa':'#059669'\" [variant]=\"'outlined'\" [size]=\"'sm'\"\r\n                        [removable]=\"true\" (removed)=\"removeFile(i)\"></ava-tag>\r\n                </div>\r\n            </ng-template>\r\n            <a class=\"viewAll\" *ngIf=\"uploadedFiles.length >2\" (click)=\"toggleViewAll()\">{{viewAll?\r\n                'View Less' : 'View All'}}</a>\r\n        </div>\r\n        <!-- <button class=\"select-files-button\" (click)=\"openFileSelector()\"\r\n            *ngIf=\"uploadedFiles.length > 0 && !singleFileMode\">\r\n            Add More Files\r\n        </button> -->\r\n        <ava-button [label]=\"'Upload'\" variant=\"primary\"\r\n            size=\"medium\" [state]=\"uploadedFiles.length > 0 ?'default':'disabled'\"\r\n            [disabled]=\"uploadedFiles.length > 0 ? false:true\" (click)=\"uploadFile()\"> </ava-button>\r\n\r\n        <!-- <button class=\"upload-button\" (click)=\"uploadFile()\">\r\n            {{uploadedFiles.length > 0 ? appConstants.UPLOAD_BUTTON : 'Upload'}}\r\n        </button> -->\r\n    </div>\r\n</div>", "import { CommonModule } from '@angular/common';\r\nimport { FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { ChangeDetectionStrategy, Component, EventEmitter, HostListener, Input, OnDestroy, OnInit, Output, ViewChild, ElementRef, forwardRef } from '@angular/core';\r\nimport { IconComponent } from '../icon/icon.component';\r\nexport interface DateRange {\r\n  start: Date | null;\r\n  end: Date | null;\r\n}\r\n\r\nexport interface CalendarDay {\r\n  date: Date;\r\n  isCurrentMonth: boolean;\r\n  isToday: boolean;\r\n  isSelected: boolean;\r\n  isInRange: boolean;\r\n  isRangeStart: boolean;\r\n  isRangeEnd: boolean;\r\n}\r\n\r\n\r\n@Component({\r\n  selector: 'ava-calendar',\r\n  imports: [CommonModule, FormsModule, IconComponent],\r\n  templateUrl: './calendar.component.html',\r\n  styleUrl: './calendar.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => CalendarComponent),\r\n      multi: true\r\n    }\r\n  ]\r\n})\r\nexport class CalendarComponent implements OnInit, OnDestroy {\r\n  @Input() isRange = false;\r\n  @Input() selectedDate: Date | null = null;\r\n  @Input() dateRange: DateRange = { start: null, end: null };\r\n  @Input() weekdayFormat: 1 | 2 | 3 = 3;\r\n  @Input() alwaysOpen = false;\r\n\r\n  @Output() dateSelected = new EventEmitter<Date>();\r\n  @Output() rangeSelected = new EventEmitter<DateRange>();\r\n\r\n  // ViewChild references for input navigation\r\n  @ViewChild('dayInput') dayInput!: ElementRef<HTMLInputElement>;\r\n  @ViewChild('monthInput') monthInput!: ElementRef<HTMLInputElement>;\r\n  @ViewChild('yearInput') yearInput!: ElementRef<HTMLInputElement>;\r\n  @ViewChild('startDayInput') startDayInput!: ElementRef<HTMLInputElement>;\r\n  @ViewChild('startMonthInput') startMonthInput!: ElementRef<HTMLInputElement>;\r\n  @ViewChild('startYearInput') startYearInput!: ElementRef<HTMLInputElement>;\r\n  @ViewChild('endDayInput') endDayInput!: ElementRef<HTMLInputElement>;\r\n  @ViewChild('endMonthInput') endMonthInput!: ElementRef<HTMLInputElement>;\r\n  @ViewChild('endYearInput') endYearInput!: ElementRef<HTMLInputElement>;\r\n\r\n  // State\r\n  isOpen = false;\r\n  currentMonth = new Date().getMonth();\r\n  currentYear = new Date().getFullYear();\r\n  hoverDate: Date | null = null;\r\n  isSelectingRangeEnd = false;\r\n  currentSegment: string = '';\r\n  selectedNavigation: 'month' | 'year' = 'month';\r\n\r\n  // Structured input values for single date\r\n  dayValue = '';\r\n  monthValue = '';\r\n  yearValue = '';\r\n\r\n  // Structured input values for range\r\n  startDayValue = '';\r\n  startMonthValue = '';\r\n  startYearValue = '';\r\n  endDayValue = '';\r\n  endMonthValue = '';\r\n  endYearValue = '';\r\n\r\n  // Constants\r\n  readonly monthNames = [\r\n    'January', 'February', 'March', 'April', 'May', 'June',\r\n    'July', 'August', 'September', 'October', 'November', 'December'\r\n  ];\r\n\r\n  private readonly weekDaysBase = [\r\n    { full: 'Monday', three: 'Mon', two: 'Mo', one: 'M' },\r\n    { full: 'Tuesday', three: 'Tue', two: 'Tu', one: 'T' },\r\n    { full: 'Wednesday', three: 'Wed', two: 'We', one: 'W' },\r\n    { full: 'Thursday', three: 'Thu', two: 'Th', one: 'T' },\r\n    { full: 'Friday', three: 'Fri', two: 'Fr', one: 'F' },\r\n    { full: 'Saturday', three: 'Sat', two: 'Sa', one: 'S' },\r\n    { full: 'Sunday', three: 'Sun', two: 'Su', one: 'S' }\r\n  ];\r\n  \r\n  private readonly today = new Date();\r\n\r\n  // ControlValueAccessor properties\r\n  value: Date | DateRange | null = null;\r\n  private onChange = (value: any) => { };\r\n  private onTouched = () => { };\r\n\r\n  // Computed properties\r\n  get weekDays(): string[] {\r\n    const formatKey = this.weekdayFormat === 1 ? 'one' :\r\n                     this.weekdayFormat === 2 ? 'two' : 'three';\r\n    return this.weekDaysBase.map(day => day[formatKey as keyof typeof day]);\r\n  }\r\n\r\n  get yearRange(): number[] {\r\n    const currentYear = new Date().getFullYear();\r\n    const start = currentYear - 50; // Show 50 years before current year\r\n    const end = currentYear + 50;   // Show 50 years after current year\r\n    return Array.from({ length: end - start + 1 }, (_, i) => start + i);\r\n  }\r\n\r\n  get calendarDays(): CalendarDay[] {\r\n    const firstDay = new Date(this.currentYear, this.currentMonth, 1);\r\n    const lastDay = new Date(this.currentYear, this.currentMonth + 1, 0);\r\n    const daysInMonth = lastDay.getDate();\r\n\r\n    // Calculate start date (Monday of the week containing the 1st)\r\n    const startDate = new Date(firstDay);\r\n    const dayOfWeek = (firstDay.getDay() + 6) % 7; // Monday = 0, Sunday = 6\r\n    startDate.setDate(firstDay.getDate() - dayOfWeek);\r\n\r\n    // Calculate how many weeks we need\r\n    const weeksNeeded = Math.ceil((daysInMonth + dayOfWeek) / 7);\r\n    const totalCells = weeksNeeded * 7;\r\n\r\n    const days: CalendarDay[] = [];\r\n\r\n    // Generate all days for the calendar grid\r\n    for (let i = 0; i < totalCells; i++) {\r\n      const date = new Date(startDate);\r\n      date.setDate(startDate.getDate() + i);\r\n\r\n      const isCurrentMonth = date.getMonth() === this.currentMonth && date.getFullYear() === this.currentYear;\r\n\r\n      days.push({\r\n        date,\r\n        isCurrentMonth,\r\n        isToday: this.isSameDay(date, this.today),\r\n        isSelected: this.isDateSelected(date),\r\n        isInRange: this.isDateInRange(date),\r\n        isRangeStart: this.isDateRangeStart(date),\r\n        isRangeEnd: this.isDateRangeEnd(date)\r\n      });\r\n    }\r\n\r\n    return days;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.updateInputValues();\r\n\r\n    // Initialize structured input values if dates are provided\r\n    if (this.isRange) {\r\n      this.updateStructuredRangeInputFromDates(this.dateRange.start, this.dateRange.end);\r\n    } else if (this.selectedDate) {\r\n      this.updateStructuredInputFromDate(this.selectedDate);\r\n    }\r\n\r\n    // Set calendar to always open if alwaysOpen is true\r\n    if (this.alwaysOpen) {\r\n      this.isOpen = true;\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Cleanup if needed\r\n  }\r\n\r\n  // ControlValueAccessor implementation\r\n  writeValue(obj: any): void {\r\n    if (this.isRange) {\r\n      if (obj && typeof obj === 'object' && obj.start !== undefined && obj.end !== undefined) {\r\n        this.dateRange = obj;\r\n        this.value = obj;\r\n        this.updateStructuredRangeInputFromDates(obj.start, obj.end);\r\n      } else {\r\n        this.dateRange = { start: null, end: null };\r\n        this.value = null;\r\n        this.updateStructuredRangeInputFromDates(null, null);\r\n      }\r\n    } else {\r\n      if (obj instanceof Date) {\r\n        this.selectedDate = obj;\r\n        this.value = obj;\r\n        this.updateStructuredInputFromDate(obj);\r\n      } else {\r\n        this.selectedDate = null;\r\n        this.value = null;\r\n        this.updateStructuredInputFromDate(null);\r\n      }\r\n    }\r\n  }\r\n\r\n  registerOnChange(fn: any): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: any): void {\r\n    this.onTouched = fn;\r\n  }\r\n\r\n  setDisabledState?(isDisabled: boolean): void {\r\n    // Implement if you want to support disabled state\r\n  }\r\n\r\n  // Public methods\r\n  toggle(): void {\r\n    this.isOpen = !this.isOpen;\r\n    if (this.isOpen) {\r\n      this.updateInputValues();\r\n    }\r\n  }\r\n\r\n  close(): void {\r\n    if (!this.alwaysOpen) {\r\n      this.isOpen = false;\r\n    }\r\n  }\r\n\r\n  navigate(direction: number): void {\r\n    if (this.selectedNavigation === 'month') {\r\n      this.navigateMonth(direction);\r\n    } else if (this.selectedNavigation === 'year') {\r\n      this.navigateYear(direction);\r\n    }\r\n  }\r\n\r\n  selectNavigation(type: 'month' | 'year'): void {\r\n    this.selectedNavigation = type;\r\n  }\r\n\r\n  onMonthYearKeyDown(event: KeyboardEvent, type: 'month' | 'year'): void {\r\n    switch (event.key) {\r\n      case 'Enter':\r\n      case ' ':\r\n        event.preventDefault();\r\n        this.selectNavigation(type);\r\n        break;\r\n      case 'ArrowLeft':\r\n        event.preventDefault();\r\n        this.selectNavigation(type);\r\n        this.navigate(-1);\r\n        break;\r\n      case 'ArrowRight':\r\n        event.preventDefault();\r\n        this.selectNavigation(type);\r\n        this.navigate(1);\r\n        break;\r\n      case 'ArrowUp':\r\n        event.preventDefault();\r\n        if (type === 'month') {\r\n          this.selectNavigation('month');\r\n          this.navigate(-1);\r\n        } else {\r\n          this.selectNavigation('year');\r\n          this.navigate(-1);\r\n        }\r\n        break;\r\n      case 'ArrowDown':\r\n        event.preventDefault();\r\n        if (type === 'month') {\r\n          this.selectNavigation('month');\r\n          this.navigate(1);\r\n        } else {\r\n          this.selectNavigation('year');\r\n          this.navigate(1);\r\n        }\r\n        break;\r\n      case 'Tab':\r\n        // Allow default tab behavior to move between month and year\r\n        break;\r\n      default:\r\n        // Ignore other keys\r\n        break;\r\n    }\r\n  }\r\n\r\n  getNavLabel(direction: number): string {\r\n    const action = direction > 0 ? 'Next' : 'Previous';\r\n    if (this.selectedNavigation === 'month') {\r\n      return `${action} month`;\r\n    } else {\r\n      return `${action} year`;\r\n    }\r\n  }\r\n\r\n  selectDate(date: Date): void {\r\n    if (this.isRange) {\r\n      this.handleRangeSelection(date);\r\n    } else {\r\n      this.handleSingleSelection(date);\r\n    }\r\n  }\r\n\r\n  onDayHover(date: Date): void {\r\n    if (this.isRange && this.dateRange.start && !this.dateRange.end) {\r\n      this.hoverDate = date;\r\n    }\r\n  }\r\n\r\n  onDayLeave(): void {\r\n    this.hoverDate = null;\r\n  }\r\n\r\n  // Structured input event handlers\r\n  onSegmentFocus(segment: string): void {\r\n    this.currentSegment = segment;\r\n  }\r\n\r\n  onSegmentBlur(segment: string): void {\r\n    this.validateAndUpdateDate();\r\n  }\r\n\r\n  onKeyDown(event: KeyboardEvent, segment: string): void {\r\n    const target = event.target as HTMLInputElement;\r\n    const value = target.value;\r\n\r\n    // Allow navigation keys\r\n    if (['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {\r\n      if (event.key === 'Enter') {\r\n        this.validateAndUpdateDate();\r\n      }\r\n      return;\r\n    }\r\n\r\n    // Only allow numbers\r\n    if (!/^\\d$/.test(event.key)) {\r\n      event.preventDefault();\r\n      return;\r\n    }\r\n\r\n    // Auto-advance to next segment when current is complete\r\n    setTimeout(() => {\r\n      this.checkAutoAdvance(segment, target.value);\r\n    }, 0);\r\n  }\r\n\r\n  onInputClick(): void {\r\n    if (!this.isOpen) {\r\n      this.toggle();\r\n    }\r\n  }\r\n\r\n  // Day input handlers\r\n  onDayInput(event: any): void {\r\n    const value = event.target.value;\r\n    this.dayValue = this.limitValue(value, 1, 31, 2);\r\n    if (this.dayValue.length === 2) {\r\n      this.focusNextSegment('day');\r\n    }\r\n  }\r\n\r\n  onMonthInput(event: any): void {\r\n    const value = event.target.value;\r\n    this.monthValue = this.limitValue(value, 1, 12, 2);\r\n    if (this.monthValue.length === 2) {\r\n      this.focusNextSegment('month');\r\n    }\r\n  }\r\n\r\n  onYearInput(event: any): void {\r\n    const value = event.target.value;\r\n    this.yearValue = this.limitValue(value, 1900, 2100, 4);\r\n    if (this.yearValue.length === 4) {\r\n      this.validateAndUpdateDate();\r\n    }\r\n  }\r\n\r\n  // Range start input handlers\r\n  onStartDayInput(event: any): void {\r\n    const value = event.target.value;\r\n    this.startDayValue = this.limitValue(value, 1, 31, 2);\r\n    if (this.startDayValue.length === 2) {\r\n      this.focusNextSegment('startDay');\r\n    }\r\n  }\r\n\r\n  onStartMonthInput(event: any): void {\r\n    const value = event.target.value;\r\n    this.startMonthValue = this.limitValue(value, 1, 12, 2);\r\n    if (this.startMonthValue.length === 2) {\r\n      this.focusNextSegment('startMonth');\r\n    }\r\n  }\r\n\r\n  onStartYearInput(event: any): void {\r\n    const value = event.target.value;\r\n    this.startYearValue = this.limitValue(value, 1900, 2100, 4);\r\n    if (this.startYearValue.length === 4) {\r\n      this.focusNextSegment('startYear');\r\n    }\r\n  }\r\n\r\n  // Range end input handlers\r\n  onEndDayInput(event: any): void {\r\n    const value = event.target.value;\r\n    this.endDayValue = this.limitValue(value, 1, 31, 2);\r\n    if (this.endDayValue.length === 2) {\r\n      this.focusNextSegment('endDay');\r\n    }\r\n  }\r\n\r\n  onEndMonthInput(event: any): void {\r\n    const value = event.target.value;\r\n    this.endMonthValue = this.limitValue(value, 1, 12, 2);\r\n    if (this.endMonthValue.length === 2) {\r\n      this.focusNextSegment('endMonth');\r\n    }\r\n  }\r\n\r\n  onEndYearInput(event: any): void {\r\n    const value = event.target.value;\r\n    this.endYearValue = this.limitValue(value, 1900, 2100, 4);\r\n    if (this.endYearValue.length === 4) {\r\n      this.validateAndUpdateDate();\r\n    }\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event): void {\r\n    if (this.isOpen && !this.isClickInside(event)) {\r\n      this.close();\r\n    }\r\n  }\r\n\r\n  // Template methods\r\n  trackByDate(_index: number, day: CalendarDay): string {\r\n    return day.date.toDateString();\r\n  }\r\n\r\n  getDayClasses(day: CalendarDay): string {\r\n    const classes = [];\r\n    if (!day.isCurrentMonth) classes.push('other-month');\r\n    if (day.isToday) classes.push('today');\r\n    if (day.isSelected) classes.push('selected');\r\n    if (day.isInRange) classes.push('in-range');\r\n    if (day.isRangeStart) classes.push('range-start');\r\n    if (day.isRangeEnd) classes.push('range-end');\r\n    return classes.join(' ');\r\n  }\r\n\r\n\r\n\r\n  formatDate(date: Date | null): string {\r\n    return date ? this.toDateString(date) : '';\r\n  }\r\n\r\n  formatDateRange(): string {\r\n    if (!this.dateRange.start) return '';\r\n    const start = this.toDateString(this.dateRange.start);\r\n    const end = this.dateRange.end ? this.toDateString(this.dateRange.end) : '';\r\n    return end ? `${start} - ${end}` : start;\r\n  }\r\n\r\n  // Helper methods for structured input\r\n  private limitValue(value: string, min: number, max: number, maxLength: number): string {\r\n    // Remove non-numeric characters\r\n    const numericValue = value.replace(/\\D/g, '');\r\n\r\n    // Limit length\r\n    const limitedValue = numericValue.slice(0, maxLength);\r\n\r\n    // Convert to number and check bounds\r\n    const num = parseInt(limitedValue, 10);\r\n    if (isNaN(num)) return '';\r\n\r\n    // For 2-digit fields, auto-pad and validate\r\n    if (maxLength === 2 && limitedValue.length === 2) {\r\n      if (num < min) return min.toString().padStart(2, '0');\r\n      if (num > max) return max.toString().padStart(2, '0');\r\n      return limitedValue.padStart(2, '0');\r\n    }\r\n\r\n    // For 4-digit year, validate range\r\n    if (maxLength === 4 && limitedValue.length === 4) {\r\n      if (num < min) return min.toString();\r\n      if (num > max) return max.toString();\r\n    }\r\n\r\n    return limitedValue;\r\n  }\r\n\r\n  private focusNextSegment(currentSegment: string): void {\r\n    const segmentMap: { [key: string]: ElementRef<HTMLInputElement> | null } = {\r\n      'day': this.monthInput,\r\n      'month': this.yearInput,\r\n      'startDay': this.startMonthInput,\r\n      'startMonth': this.startYearInput,\r\n      'startYear': this.endDayInput,\r\n      'endDay': this.endMonthInput,\r\n      'endMonth': this.endYearInput\r\n    };\r\n\r\n    const nextInput = segmentMap[currentSegment];\r\n    if (nextInput) {\r\n      setTimeout(() => {\r\n        nextInput.nativeElement.focus();\r\n        nextInput.nativeElement.select();\r\n      }, 0);\r\n    }\r\n  }\r\n\r\n  private checkAutoAdvance(segment: string, value: string): void {\r\n    const maxLengths: { [key: string]: number } = {\r\n      'day': 2, 'month': 2, 'year': 4,\r\n      'startDay': 2, 'startMonth': 2, 'startYear': 4,\r\n      'endDay': 2, 'endMonth': 2, 'endYear': 4\r\n    };\r\n\r\n    if (value.length >= maxLengths[segment]) {\r\n      this.focusNextSegment(segment);\r\n    }\r\n  }\r\n\r\n  private validateAndUpdateDate(): void {\r\n    if (this.isRange) {\r\n      this.validateRangeDate();\r\n    } else {\r\n      this.validateSingleDate();\r\n    }\r\n  }\r\n\r\n  private validateSingleDate(): void {\r\n    if (this.dayValue && this.monthValue && this.yearValue) {\r\n      const day = parseInt(this.dayValue, 10);\r\n      const month = parseInt(this.monthValue, 10);\r\n      const year = parseInt(this.yearValue, 10);\r\n\r\n      const date = new Date(year, month - 1, day);\r\n      if (this.isValidDate(date, day, month - 1, year)) {\r\n        this.selectedDate = date;\r\n        this.value = date;\r\n        this.currentMonth = date.getMonth();\r\n        this.currentYear = date.getFullYear();\r\n        this.dateSelected.emit(date);\r\n        this.onChange(date);\r\n        this.onTouched();\r\n      }\r\n    }\r\n  }\r\n\r\n  private validateRangeDate(): void {\r\n    // Validate start date\r\n    if (this.startDayValue && this.startMonthValue && this.startYearValue) {\r\n      const startDay = parseInt(this.startDayValue, 10);\r\n      const startMonth = parseInt(this.startMonthValue, 10);\r\n      const startYear = parseInt(this.startYearValue, 10);\r\n\r\n      const startDate = new Date(startYear, startMonth - 1, startDay);\r\n      if (this.isValidDate(startDate, startDay, startMonth - 1, startYear)) {\r\n        this.dateRange.start = startDate;\r\n        this.currentMonth = startDate.getMonth();\r\n        this.currentYear = startDate.getFullYear();\r\n      }\r\n    }\r\n\r\n    // Validate end date\r\n    if (this.endDayValue && this.endMonthValue && this.endYearValue) {\r\n      const endDay = parseInt(this.endDayValue, 10);\r\n      const endMonth = parseInt(this.endMonthValue, 10);\r\n      const endYear = parseInt(this.endYearValue, 10);\r\n\r\n      const endDate = new Date(endYear, endMonth - 1, endDay);\r\n      if (this.isValidDate(endDate, endDay, endMonth - 1, endYear)) {\r\n        this.dateRange.end = endDate;\r\n\r\n        // Emit range if both dates are valid\r\n        if (this.dateRange.start && this.dateRange.end) {\r\n          this.value = this.dateRange;\r\n          this.rangeSelected.emit(this.dateRange);\r\n          this.onChange(this.dateRange);\r\n          this.onTouched();\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // Private methods\r\n  private navigateMonth(direction: number): void {\r\n    const newDate = new Date(this.currentYear, this.currentMonth + direction, 1);\r\n    this.currentMonth = newDate.getMonth();\r\n    this.currentYear = newDate.getFullYear();\r\n  }\r\n\r\n  private navigateYear(direction: number): void {\r\n    this.currentYear += direction;\r\n    // Ensure year stays within reasonable bounds\r\n    if (this.currentYear < 1900) this.currentYear = 1900;\r\n    if (this.currentYear > 2100) this.currentYear = 2100;\r\n  }\r\n\r\n  private handleSingleSelection(date: Date): void {\r\n    this.selectedDate = date;\r\n    this.value = date;\r\n    this.updateStructuredInputFromDate(date);\r\n    this.dateSelected.emit(date);\r\n    this.onChange(date);\r\n    this.onTouched();\r\n    this.close();\r\n  }\r\n\r\n  private handleRangeSelection(date: Date): void {\r\n    if (!this.dateRange.start || (this.dateRange.start && this.dateRange.end)) {\r\n      this.startNewRange(date);\r\n    } else {\r\n      this.completeRange(date);\r\n    }\r\n  }\r\n\r\n  private startNewRange(date: Date): void {\r\n    this.dateRange = { start: date, end: null };\r\n    this.updateStructuredRangeInputFromDates(date, null);\r\n    this.isSelectingRangeEnd = true;\r\n  }\r\n\r\n  private completeRange(date: Date): void {\r\n    const start = this.dateRange.start!;\r\n    if (date < start) {\r\n      this.dateRange = { start: date, end: start };\r\n    } else {\r\n      this.dateRange.end = date;\r\n    }\r\n    this.value = this.dateRange;\r\n    this.updateStructuredRangeInputFromDates(this.dateRange.start, this.dateRange.end);\r\n    this.rangeSelected.emit(this.dateRange);\r\n    this.onChange(this.dateRange);\r\n    this.onTouched();\r\n    this.isSelectingRangeEnd = false;\r\n    this.close();\r\n  }\r\n\r\n  private updateStructuredInputFromDate(date: Date | null): void {\r\n    if (date) {\r\n      this.dayValue = date.getDate().toString().padStart(2, '0');\r\n      this.monthValue = (date.getMonth() + 1).toString().padStart(2, '0');\r\n      this.yearValue = date.getFullYear().toString();\r\n    } else {\r\n      this.dayValue = '';\r\n      this.monthValue = '';\r\n      this.yearValue = '';\r\n    }\r\n  }\r\n\r\n  private updateStructuredRangeInputFromDates(startDate: Date | null, endDate: Date | null): void {\r\n    if (startDate) {\r\n      this.startDayValue = startDate.getDate().toString().padStart(2, '0');\r\n      this.startMonthValue = (startDate.getMonth() + 1).toString().padStart(2, '0');\r\n      this.startYearValue = startDate.getFullYear().toString();\r\n    } else {\r\n      this.startDayValue = '';\r\n      this.startMonthValue = '';\r\n      this.startYearValue = '';\r\n    }\r\n\r\n    if (endDate) {\r\n      this.endDayValue = endDate.getDate().toString().padStart(2, '0');\r\n      this.endMonthValue = (endDate.getMonth() + 1).toString().padStart(2, '0');\r\n      this.endYearValue = endDate.getFullYear().toString();\r\n    } else {\r\n      this.endDayValue = '';\r\n      this.endMonthValue = '';\r\n      this.endYearValue = '';\r\n    }\r\n  }\r\n\r\n  private updateInputValues(): void {\r\n    if (this.isRange) {\r\n      this.updateStructuredRangeInputFromDates(this.dateRange.start, this.dateRange.end);\r\n    } else {\r\n      this.updateStructuredInputFromDate(this.selectedDate);\r\n    }\r\n  }\r\n\r\n  private parseDate(dateStr: string): Date | null {\r\n    if (!dateStr) return null;\r\n    \r\n    const parts = dateStr.split('/');\r\n    if (parts.length !== 3) return null;\r\n    \r\n    const [day, month, year] = parts.map(p => parseInt(p, 10));\r\n    if ([day, month, year].some(isNaN)) return null;\r\n    \r\n    const date = new Date(year, month - 1, day);\r\n    return this.isValidDate(date, day, month - 1, year) ? date : null;\r\n  }\r\n\r\n  private isValidDate(date: Date, day: number, month: number, year: number): boolean {\r\n    return date.getDate() === day && \r\n           date.getMonth() === month && \r\n           date.getFullYear() === year &&\r\n           year >= 1900 && year <= 2100;\r\n  }\r\n\r\n  private toDateString(date: Date): string {\r\n    return [\r\n      date.getDate().toString().padStart(2, '0'),\r\n      (date.getMonth() + 1).toString().padStart(2, '0'),\r\n      date.getFullYear()\r\n    ].join('/');\r\n  }\r\n\r\n  private isSameDay(date1: Date, date2: Date): boolean {\r\n    return date1.toDateString() === date2.toDateString();\r\n  }\r\n\r\n  private isDateSelected(date: Date): boolean {\r\n    return !this.isRange && this.selectedDate ? \r\n           this.isSameDay(date, this.selectedDate) : false;\r\n  }\r\n\r\n  private isDateInRange(date: Date): boolean {\r\n    if (!this.isRange || !this.dateRange.start) return false;\r\n    \r\n    const end = this.dateRange.end || this.hoverDate;\r\n    if (!end) return false;\r\n    \r\n    const [rangeStart, rangeEnd] = this.dateRange.start < end ? \r\n                                   [this.dateRange.start, end] : \r\n                                   [end, this.dateRange.start];\r\n    \r\n    return date > rangeStart && date < rangeEnd;\r\n  }\r\n\r\n  private isDateRangeStart(date: Date): boolean {  \r\n    return this.isRange && this.dateRange.start ? \r\n           this.isSameDay(date, this.dateRange.start) : false;\r\n  }\r\n\r\n  private isDateRangeEnd(date: Date): boolean {\r\n    return this.isRange && this.dateRange.end ? \r\n           this.isSameDay(date, this.dateRange.end) : false;\r\n  }\r\n\r\n  private isClickInside(event: Event): boolean {\r\n    const target = event.target as Element;\r\n    return target.closest('.date-picker') !== null;\r\n  }\r\n}", " <div class=\"date-picker\" [class.open]=\"isOpen\" [class.always-open]=\"alwaysOpen\">\r\n      <!-- Input Fields (hidden when alwaysOpen is true) -->\r\n      <div class=\"input-wrapper\" *ngIf=\"!alwaysOpen\">\r\n        <ng-container *ngTemplateOutlet=\"isRange ? rangeInputs : singleInput\"></ng-container>\r\n      </div>\r\n\r\n      <!-- Calendar Popup -->\r\n      <div class=\"calendar-popup\" [class.embedded]=\"alwaysOpen\" *ngIf=\"isOpen\" (click)=\"$event.stopPropagation()\">\r\n        <!-- Navigation Header -->\r\n        <header class=\"calendar-header\">\r\n          <div class=\"month-year-display\">\r\n            <button class=\"month-selector\"\r\n                    [class.selected]=\"selectedNavigation === 'month'\"\r\n                    (click)=\"selectNavigation('month')\"\r\n                    (keydown)=\"onMonthYearKeyDown($event, 'month')\"\r\n                    [attr.aria-label]=\"'Select month: ' + monthNames[currentMonth]\"\r\n                    [attr.aria-pressed]=\"selectedNavigation === 'month'\"\r\n                    tabindex=\"0\">\r\n              {{ monthNames[currentMonth] }}\r\n            </button>\r\n            <button class=\"year-selector\"\r\n                    [class.selected]=\"selectedNavigation === 'year'\"\r\n                    (click)=\"selectNavigation('year')\"\r\n                    (keydown)=\"onMonthYearKeyDown($event, 'year')\"\r\n                    [attr.aria-label]=\"'Select year: ' + currentYear\"\r\n                    [attr.aria-pressed]=\"selectedNavigation === 'year'\"\r\n                    tabindex=\"0\">\r\n              {{ currentYear }}\r\n            </button>\r\n          </div>\r\n          <div class=\"nav-controls\">\r\n            <button class=\"nav-btn\" (click)=\"navigate(-1)\" [attr.aria-label]=\"getNavLabel(-1)\">\r\n              <ava-icon iconName=\"ChevronLeft\" [iconSize]=\"18\"></ava-icon>\r\n            </button>\r\n            <button class=\"nav-btn\" (click)=\"navigate(1)\" [attr.aria-label]=\"getNavLabel(1)\">\r\n              <ava-icon iconName=\"ChevronRight\" [iconSize]=\"18\"></ava-icon>\r\n            </button>\r\n          </div>\r\n        </header>\r\n\r\n        <!-- Calendar Grid -->\r\n        <div class=\"calendar-grid\">\r\n          <div class=\"weekdays\">\r\n            <span *ngFor=\"let day of weekDays\" class=\"weekday\">{{ day }}</span>\r\n          </div>\r\n          <div class=\"days\">\r\n            <button *ngFor=\"let day of calendarDays; trackBy: trackByDate\"\r\n                    class=\"day\"\r\n                    [class]=\"getDayClasses(day)\"\r\n                    (click)=\"selectDate(day.date)\"\r\n                    (mouseenter)=\"onDayHover(day.date)\"\r\n                    (mouseleave)=\"onDayLeave()\">\r\n              {{ day.date.getDate() }}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Templates -->\r\n    <ng-template #singleInput>\r\n      <div class=\"input-group\">\r\n        <div class=\"structured-input\" (click)=\"onInputClick()\">\r\n          <input #dayInput\r\n                 class=\"date-segment day-segment\"\r\n                 [(ngModel)]=\"dayValue\"\r\n                 placeholder=\"DD\"\r\n                 maxlength=\"2\"\r\n                 (input)=\"onDayInput($event)\"\r\n                 (keydown)=\"onKeyDown($event, 'day')\"\r\n                 (focus)=\"onSegmentFocus('day')\"\r\n                 (blur)=\"onSegmentBlur('day')\">\r\n          <span class=\"separator\">/</span>\r\n          <input #monthInput\r\n                 class=\"date-segment month-segment\"\r\n                 [(ngModel)]=\"monthValue\"\r\n                 placeholder=\"MM\"\r\n                 maxlength=\"2\"\r\n                 (input)=\"onMonthInput($event)\"\r\n                 (keydown)=\"onKeyDown($event, 'month')\"\r\n                 (focus)=\"onSegmentFocus('month')\"\r\n                 (blur)=\"onSegmentBlur('month')\">\r\n          <span class=\"separator\">/</span>\r\n          <input #yearInput\r\n                 class=\"date-segment year-segment\"\r\n                 [(ngModel)]=\"yearValue\"\r\n                 placeholder=\"YYYY\"\r\n                 maxlength=\"4\"\r\n                 (input)=\"onYearInput($event)\"\r\n                 (keydown)=\"onKeyDown($event, 'year')\"\r\n                 (focus)=\"onSegmentFocus('year')\"\r\n                 (blur)=\"onSegmentBlur('year')\">\r\n        </div>\r\n        <button class=\"input-btn\" (click)=\"toggle()\">\r\n          <ava-icon iconName=\"CalendarDays\" [iconSize]=\"16\"></ava-icon>\r\n        </button>\r\n      </div>\r\n    </ng-template>\r\n\r\n    <ng-template #rangeInputs>\r\n      <div class=\"input-group\">\r\n        <div class=\"structured-input range-structured\" (click)=\"onInputClick()\">\r\n          <!-- Start Date -->\r\n          <div class=\"date-part\">\r\n            <input #startDayInput\r\n                   class=\"date-segment day-segment\"\r\n                   [(ngModel)]=\"startDayValue\"\r\n                   placeholder=\"DD\"\r\n                   maxlength=\"2\"\r\n                   (input)=\"onStartDayInput($event)\"\r\n                   (keydown)=\"onKeyDown($event, 'startDay')\"\r\n                   (focus)=\"onSegmentFocus('startDay')\"\r\n                   (blur)=\"onSegmentBlur('startDay')\">\r\n            <span class=\"separator\">/</span>\r\n            <input #startMonthInput\r\n                   class=\"date-segment month-segment\"\r\n                   [(ngModel)]=\"startMonthValue\"\r\n                   placeholder=\"MM\"\r\n                   maxlength=\"2\"\r\n                   (input)=\"onStartMonthInput($event)\"\r\n                   (keydown)=\"onKeyDown($event, 'startMonth')\"\r\n                   (focus)=\"onSegmentFocus('startMonth')\"\r\n                   (blur)=\"onSegmentBlur('startMonth')\">\r\n            <span class=\"separator\">/</span>\r\n            <input #startYearInput\r\n                   class=\"date-segment year-segment\"\r\n                   [(ngModel)]=\"startYearValue\"\r\n                   placeholder=\"YYYY\"\r\n                   maxlength=\"4\"\r\n                   (input)=\"onStartYearInput($event)\"\r\n                   (keydown)=\"onKeyDown($event, 'startYear')\"\r\n                   (focus)=\"onSegmentFocus('startYear')\"\r\n                   (blur)=\"onSegmentBlur('startYear')\">\r\n          </div>\r\n\r\n          <span class=\"range-separator\"> - </span>\r\n\r\n          <!-- End Date -->\r\n          <div class=\"date-part\">\r\n            <input #endDayInput\r\n                   class=\"date-segment day-segment\"\r\n                   [(ngModel)]=\"endDayValue\"\r\n                   placeholder=\"DD\"\r\n                   maxlength=\"2\"\r\n                   (input)=\"onEndDayInput($event)\"\r\n                   (keydown)=\"onKeyDown($event, 'endDay')\"\r\n                   (focus)=\"onSegmentFocus('endDay')\"\r\n                   (blur)=\"onSegmentBlur('endDay')\">\r\n            <span class=\"separator\">/</span>\r\n            <input #endMonthInput\r\n                   class=\"date-segment month-segment\"\r\n                   [(ngModel)]=\"endMonthValue\"\r\n                   placeholder=\"MM\"\r\n                   maxlength=\"2\"\r\n                   (input)=\"onEndMonthInput($event)\"\r\n                   (keydown)=\"onKeyDown($event, 'endMonth')\"\r\n                   (focus)=\"onSegmentFocus('endMonth')\"\r\n                   (blur)=\"onSegmentBlur('endMonth')\">\r\n            <span class=\"separator\">/</span>\r\n            <input #endYearInput\r\n                   class=\"date-segment year-segment\"\r\n                   [(ngModel)]=\"endYearValue\"\r\n                   placeholder=\"YYYY\"\r\n                   maxlength=\"4\"\r\n                   (input)=\"onEndYearInput($event)\"\r\n                   (keydown)=\"onKeyDown($event, 'endYear')\"\r\n                   (focus)=\"onSegmentFocus('endYear')\"\r\n                   (blur)=\"onSegmentBlur('endYear')\">\r\n          </div>\r\n        </div>\r\n        <button class=\"input-btn\" (click)=\"toggle()\">\r\n          <ava-icon iconName=\"CalendarDays\" [iconSize]=\"16\"></ava-icon>\r\n        </button>\r\n      </div>\r\n    </ng-template>", "import { CommonModule } from '@angular/common';\r\nimport {\r\n  Component,\r\n  Input,\r\n  Output,\r\n  EventEmitter,\r\n  ChangeDetectionStrategy,\r\n  ViewEncapsulation,\r\n  ElementRef,\r\n  signal,\r\n  HostListener\r\n} from '@angular/core';\r\nimport { IconComponent } from '../icon/icon.component';\r\nimport { ButtonComponent } from '../button/button.component';\r\n\r\nexport interface FileAttachOption {\r\n  name: string;\r\n  icon: string;\r\n  value: string;\r\n  useCustomIcon?: boolean; // If true, 'icon' is treated as URL/path\r\n}\r\n\r\n@Component({\r\n  selector: 'ava-file-attach-pill',\r\n  imports: [IconComponent, CommonModule],\r\n  templateUrl: './file-attach-pill.component.html',\r\n  styleUrl: './file-attach-pill.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n  encapsulation: ViewEncapsulation.None,\r\n  host: {\r\n    '[class.theme-dark]': 'currentTheme === \"dark\"',\r\n    '[class.theme-light]': 'currentTheme === \"light\"'\r\n  }\r\n})\r\nexport class FileAttachPillComponent {\r\n  @Input() options: FileAttachOption[] = [\r\n    { name: 'From Computer', icon: 'upload', value: 'computer' },\r\n    { name: 'From Cloud', icon: 'cloud-upload', value: 'cloud' },\r\n    { name: 'From URL', icon: 'link', value: 'url' }\r\n  ];\r\n  \r\n  @Input() mainIcon = 'paperclip';\r\n  @Input() useCustomMainIcon = false; \r\n  @Input() mainText = 'Attach File';\r\n  @Input() currentTheme: 'light' | 'dark' = 'light';\r\n  @Input() iconSize = 20;\r\n  \r\n  @Output() optionSelected = new EventEmitter<FileAttachOption>();\r\n  \r\n  isHovered = signal(false);\r\n  isDropdownOpen = signal(false);\r\n  private isMouseOverDropdown = false;\r\n  \r\n  constructor(private elementRef: ElementRef) {}\r\n  \r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: MouseEvent): void {\r\n    if (!this.elementRef.nativeElement.contains(event.target)) {\r\n      this.closeDropdown();\r\n    }\r\n  }\r\n  \r\n  onMouseEnter(): void {\r\n    this.isHovered.set(true);\r\n  }\r\n\r\n  onMouseLeave(): void {\r\n    this.isHovered.set(false);\r\n    // Close dropdown after a small delay to allow moving to dropdown\r\n    setTimeout(() => {\r\n      if (!this.isMouseOverDropdown) {\r\n        this.closeDropdown();\r\n      }\r\n    }, 100);\r\n  }\r\n\r\n  onDropdownMouseEnter(): void {\r\n    this.isMouseOverDropdown = true;\r\n  }\r\n\r\n  onDropdownMouseLeave(): void {\r\n    this.isMouseOverDropdown = false;\r\n    this.closeDropdown();\r\n  }\r\n  \r\n  toggleDropdown(event: Event): void {\r\n    event.stopPropagation();\r\n    this.isDropdownOpen.update(value => !value);\r\n  }\r\n  \r\n  selectOption(option: FileAttachOption, event: Event): void {\r\n    event.stopPropagation();\r\n    this.optionSelected.emit(option);\r\n    this.closeDropdown();\r\n  }\r\n\r\n  private closeDropdown(): void {\r\n    this.isDropdownOpen.set(false);\r\n    this.isHovered.set(false);\r\n  }\r\n\r\n  get iconColor(): string {\r\n    return this.currentTheme === 'dark' ? 'white' : 'gray';\r\n  }\r\n\r\n  trackByOptionValue(index: number, option: FileAttachOption): string {\r\n    return option.value;\r\n  }\r\n\r\n  isCustomIcon(option?: FileAttachOption): boolean {\r\n    if (option) {\r\n      return option.useCustomIcon || false;\r\n    }\r\n    return this.useCustomMainIcon;\r\n  }\r\n}", "<div\r\n  class=\"file-attach-pill-container\"\r\n  (mouseenter)=\"onMouseEnter()\"\r\n  (mouseleave)=\"onMouseLeave()\"\r\n>\r\n  <button\r\n    class=\"file-attach-pill\"\r\n    [class.expanded]=\"isHovered()\"\r\n    (click)=\"toggleDropdown($event)\"\r\n    [attr.aria-label]=\"mainText\"\r\n    [attr.aria-expanded]=\"isDropdownOpen()\"\r\n    [attr.aria-haspopup]=\"true\"\r\n  >\r\n    <span class=\"icon-wrapper\">\r\n      <!-- Use ava-icon library -->\r\n      <ava-icon \r\n        *ngIf=\"!isCustomIcon()\"\r\n        [iconName]=\"mainIcon\" \r\n        [iconColor]=\"iconColor\"\r\n        [iconSize]=\"iconSize\">\r\n      </ava-icon>\r\n      \r\n      <!-- Use custom icon (URL/path) -->\r\n      <img \r\n        *ngIf=\"isCustomIcon()\"\r\n        [src]=\"mainIcon\" \r\n        [alt]=\"mainText\"\r\n        [style.width.px]=\"iconSize\"\r\n        [style.height.px]=\"iconSize\"\r\n        class=\"custom-icon\">\r\n    </span>\r\n    \r\n    <span class=\"text\" *ngIf=\"isHovered()\">{{ mainText }}</span>\r\n    \r\n    <span class=\"arrow\" *ngIf=\"isHovered()\">\r\n      <ava-icon \r\n        [iconName]=\"'Upload'\" \r\n        [iconColor]=\"iconColor\"\r\n        [iconSize]=\"16\">\r\n      </ava-icon>\r\n    </span>\r\n  </button>\r\n\r\n  <div\r\n    class=\"dropdown\"\r\n    [class.show]=\"isDropdownOpen()\"\r\n    role=\"menu\"\r\n    (mouseenter)=\"onDropdownMouseEnter()\"\r\n    (mouseleave)=\"onDropdownMouseLeave()\"\r\n  >\r\n    <div\r\n      *ngFor=\"let option of options; trackBy: trackByOptionValue\"\r\n      class=\"dropdown-item\"\r\n      role=\"menuitem\"\r\n      tabindex=\"0\"\r\n      (click)=\"selectOption(option, $event)\"\r\n      (keydown.enter)=\"selectOption(option, $event)\"\r\n      (keydown.space)=\"selectOption(option, $event)\"\r\n    >\r\n      <span class=\"dropdown-item-text\">{{ option.name }}</span>\r\n      <span class=\"dropdown-item-icon\">\r\n        <!-- Use ava-icon library -->\r\n        <ava-icon \r\n          *ngIf=\"!isCustomIcon(option)\"\r\n          [iconName]=\"option.icon\" \r\n          [iconColor]=\"iconColor\"\r\n          [iconSize]=\"iconSize\">\r\n        </ava-icon>\r\n        \r\n        <!-- Use custom icon (URL/path) -->\r\n        <img \r\n          *ngIf=\"isCustomIcon(option)\"\r\n          [src]=\"option.icon\" \r\n          [alt]=\"option.name\"\r\n          [style.width.px]=\"iconSize\"\r\n          [style.height.px]=\"iconSize\"\r\n          class=\"custom-icon\">\r\n      </span>\r\n    </div>\r\n  </div>\r\n</div>", "// snackbar.service.ts\r\nimport { Injectable, signal } from '@angular/core';\r\n\r\nexport type SnackbarPosition =\r\n  | 'top-left'\r\n  | 'top-right'\r\n  | 'bottom-left'\r\n  | 'bottom-right'\r\n  | 'top-center'\r\n  | 'bottom-center'\r\n  | 'center';\r\n\r\nexport interface SnackbarData {\r\n  message: string;\r\n  duration: number;\r\n  position: SnackbarPosition;\r\n  color: string;\r\n  backgroundColor: string;\r\n}\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class SnackbarService {\r\n  private snackbarSignal = signal<SnackbarData | null>(null);\r\n  readonly snackbar$ = this.snackbarSignal.asReadonly();\r\n\r\n  show(\r\n    message: string,\r\n    position: SnackbarPosition = 'bottom-center',\r\n    duration = 3000,\r\n    color = '#fff',\r\n    backgroundColor = '#6B7280'\r\n  ) {\r\n    this.snackbarSignal.set({\r\n      message,\r\n      duration,\r\n      position,\r\n      color,\r\n      backgroundColor\r\n    });\r\n\r\n    // Clear after duration\r\n    setTimeout(() => {\r\n      this.snackbarSignal.set(null);\r\n    }, duration);\r\n  }\r\n}\r\n", "import { CommonModule } from '@angular/common';\r\nimport { Component, computed, effect, inject, signal } from '@angular/core';\r\nimport { SnackbarService } from './snackbar.service';\r\n\r\n@Component({\r\n  selector: 'ava-snackbar',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './snackbar.component.html',\r\n  styleUrls: ['./snackbar.component.scss'],\r\n})\r\nexport class SnackbarComponent {\r\n  snackbarService = inject(SnackbarService);\r\n\r\n  // Directly expose the readonly signal to the template\r\n  snackbar$ = this.snackbarService.snackbar$;\r\n}\r\n", "<div *ngIf=\"snackbar$()\" class=\"ava-snackbar\" [ngClass]=\"snackbar$()?.position\" [ngStyle]=\"{\r\n       'color': snackbar$()?.color,\r\n       'background-color': snackbar$()?.backgroundColor\r\n     }\">\r\n    {{ snackbar$()?.message }}\r\n</div>", "/*\r\n * Public API Surface of play-comp-library\r\n */\r\n\r\n// src/public-api.ts\r\n\r\nexport * from './lib/components/button/button.component';\r\nexport * from './lib/components/icon/icon.component';\r\nexport * from './lib/components/checkbox/checkbox.component';\r\nexport * from './lib/components/toggle/toggle.component';\r\nexport * from './lib/components/tabs/tabs.component';\r\n\r\nexport * from './lib/components/pagination-controls/pagination-controls.component';\r\nexport * from './lib/components/accordion/accordion.component';\r\nexport * from './lib/components/textbox/ava-textbox.component';\r\nexport * from './lib/components/textarea/ava-textarea.component';\r\nexport * from './lib/components/avatars/avatars.component';\r\nexport * from './lib/components/badges/badges.component';\r\nexport * from './lib/components/spinner/spinner.component';\r\nexport * from './lib/components/card/card.component';\r\nexport * from './lib/components/feature-card/feature-card.component';\r\nexport * from './lib/components/advanced-card/advanced-card.component';\r\nexport * from './lib/components/popup/popup.component';\r\n\r\nexport * from './lib/components/link/link.component';\r\nexport * from './lib/composite-components/approval-card/approval-card.component';\r\nexport * from './lib/components/badges/badges.component';\r\nexport * from './lib/composite-components/image-card/image-card.component';\r\nexport * from './lib/composite-components/text-card/text-card.component';\r\nexport * from './lib/components/dropdown/dropdown.component';\r\nexport * from './lib/components/avatars/avatars.component';\r\nexport * from './lib/components/sidebar/sidebar.component';\r\nexport * from './lib/components/slider/slider.component';\r\n\r\nexport * from './lib/composite-components/confirmation-popup/confirmation-popup.component';\r\n\r\nexport * from './lib/components/fileupload/fileupload.component';\r\nexport * from './lib/components/calendar/calendar.component';\r\nexport * from './lib/components/file-attach-pill/file-attach-pill.component';\r\n\r\nexport * from './lib/components/snackbar/snackbar.service';\r\nexport * from './lib/components/snackbar/snackbar.component'\r\n\r\n/*\r\n\r\nexport * from './lib/components/slider/slider.component';\r\n\r\nexport * from './lib/components/input/input.component';\r\nexport * from './lib/components/link/link.component';\r\n\r\nexport * from './lib/components/heading/heading.component';\r\nexport * from './lib/components/radio-button/radio-button.component';\r\nexport * from './lib/components/labels/labels.component';\r\nexport * from './lib/components/caption/caption.component';\r\nexport * from './lib/components/dividers/dividers.component';\r\nexport * from './lib/components/tooltip/tooltip.component';\r\nexport * from './lib/components/spinner/spinner.component';\r\nexport * from './lib/components/datepicker/datepicker.component';\r\nexport * from './lib/components/dropdown/dropdown.component';\r\nexport * from './lib/components/pop-up/pop-up.component';\r\n\r\nexport * from './lib/components/search-fields/search-fields.component';\r\nexport * from './lib/components/progress/progress.component';\r\nexport * from './lib/components/toast-messages/toast-messages.component';\r\nexport * from './lib/components/inline/inline.component';\r\nexport * from './lib/components/time-picker/time-picker.component';\r\nexport * from './lib/components/breadcrumbs/breadcrumbs.component';\r\nexport * from './lib/components/header/header.component';\r\nexport * from './lib/components/snackbar/snackbar.component';\r\nexport * from './lib/components/footer/footer.component';\r\nexport * from './lib/components/input-groups/input-groups.component';\r\nexport * from './lib/components/table-header/table-header.component';\r\nexport * from './lib/components/table-content/table-content.component';\r\nexport * from './lib/components/fileupload/fileupload.component';\r\n// export * from './lib/components/grid/grid.component';\r\nexport * from './lib/components/cards/cards.component';\r\n\r\nexport * from './lib/components/authentication/authentication.component';\r\nexport * from './lib/components/body-text/body-text.component';\r\nexport * from './lib/components/modal/modal.component';\r\nexport * from './lib/components/prompt-bar/prompt-bar.component';\r\nexport * from './lib/components/stepper/stepper.component';\r\n\r\nexport * from './lib/components/chat-window/chat-window.component';\r\nexport * from './lib/components/navbar/navbar.component';\r\nexport * from \"./lib/components/experience-history-cards/experience-history-cards.component\";\r\nexport * from './lib/components/split-screen/split-screen.component';\r\nexport * from './lib/components/split-screen/panel-components/left-panel.component';\r\nexport * from './lib/components/split-screen/panel-components/right-panel.component';\r\nexport * from './lib/components/icon-pill/icon-pill.component';\r\nexport * from './lib/components/charts/charts.component';\r\nexport * from './lib/components/tags/tags.component';\r\n*/\r\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i2"], "mappings": ";;;;;;;;;;MAkBa,aAAa,CAAA;IACf,QAAQ,GAAG,EAAE;IACb,KAAK,GAAG,EAAE;IACV,QAAQ,GAAG,KAAK;IAChB,SAAS,GAAG,SAAS;IACrB,QAAQ,GAAoB,EAAE;IAC9B,MAAM,GAAG,KAAK;AACb,IAAA,SAAS,GAAG,IAAI,YAAY,EAAS;AAG/C,IAAA,IAAI,aAAa,GAAA;QACf,IAAI,IAAI,CAAC,QAAQ;AACf,YAAA,OAAO,mCAAmC;QAC5C,OAAO,IAAI,CAAC,SAAS;;AAEvB,IAAA,WAAW,CAAC,KAAY,EAAA;QACtB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACjC,KAAK,CAAC,cAAc,EAAE;YACtB;;AAEF,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;;wGApBjB,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,aAAa,EClB1B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,OAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,6FAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,uPAEU,EDME,MAAA,EAAA,CAAA,sRAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,4HAAE,mBAAmB,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,sBAAA,EAAA,QAAA,EAAA,oDAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;4FAUhC,aAAa,EAAA,UAAA,EAAA,CAAA;kBAZzB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,UAAU,EACX,OAAA,EAAA,CAAC,YAAY,EAAE,mBAAmB,CAAC,EAAA,eAAA,EAG3B,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAC/B,IAAA,EAAA;AACJ,wBAAA,mBAAmB,EAAE,UAAU;AAC/B,wBAAA,OAAO,EAAE;AACV,qBAAA,EAAA,QAAA,EAAA,uPAAA,EAAA,MAAA,EAAA,CAAA,sRAAA,CAAA,EAAA;8BAGQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACS,SAAS,EAAA,CAAA;sBAAlB;;;MEMU,eAAe,CAAA;IACjB,KAAK,GAAG,EAAE;IACV,OAAO,GAAkB,SAAS;IAClC,IAAI,GAAe,QAAQ;IAC3B,KAAK,GAAgB,SAAS;IAC9B,MAAM,GAA+B,QAAQ;IAC7C,IAAI,GAAG,KAAK;IACZ,QAAQ,GAAG,KAAK;AAChB,IAAA,KAAK;AACL,IAAA,MAAM;AACN,IAAA,QAAQ;AACR,IAAA,UAAU;AACV,IAAA,KAAK;IACL,QAAQ,GAAG,KAAK;IAGhB,QAAQ,GAAG,EAAE;IACb,SAAS,GAAG,EAAE;IACd,QAAQ,GAAG,EAAE;IACb,YAAY,GAA8B,MAAM;AAG/C,IAAA,SAAS,GAAG,IAAI,YAAY,EAAS;IAG/C,QAAQ,GAAG,KAAK;AAChB,IAAA,UAAU;IACV,eAAe,GAAG,OAAO;IACzB,QAAQ,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;AACzB,YAAA,IAAI,CAAC,eAAe,GAAG,qBAAqB;;aACvC;AACL,YAAA,IAAI,CAAC,eAAe,GAAG,kBAAkB;;AAE3C,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,KAAK,QAAQ,GAAG,IAAI,GAAG,KAAK;;AAExD,IAAA,WAAW,CAAC,KAAY,EAAA;AACtB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,KAAK,CAAC,cAAc,EAAE;YACtB;;QAEF,IAAI,CAAC,cAAc,EAAE;AACrB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;;AAG5B,IAAA,SAAS,CAAC,KAAoB,EAAA;AAC5B,QAAA,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;YAC9C,KAAK,CAAC,cAAc,EAAE;AACtB,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,IAAI,CAAC,cAAc,EAAE;AACrB,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;;;;IAIhC,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;AACpB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,MAAK;AAChC,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;SACtB,EAAE,GAAG,CAAC;;AAGT,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ;;AAGxB,IAAA,IAAI,iBAAiB,GAAA;QACnB,IAAI,IAAI,CAAC,QAAQ;AACf,YAAA,OAAO,mCAAmC;QAC5C,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;YACrD,OAAO,IAAI,CAAC,SAAS;AACvB,QAAA,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS;AAC5B,YAAA,OAAO,4BAA4B;AACrC,QAAA,OAAO,8BAA8B;;AAGvC,IAAA,YAAY,CAAC,KAAa,EAAA;AACxB,QAAA,MAAM,CAAC,GAAG,IAAI,MAAM,EAAE,CAAC,KAAK;AAC5B,QAAA,CAAC,CAAC,KAAK,GAAG,KAAK;AACf,QAAA,OAAO,CAAC,CAAC,KAAK,KAAK,EAAE;;IAIvB,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC;;;wGApFtB,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,eAAe,ycC/B5B,qhDAkCS,EAAA,MAAA,EAAA,CAAA,y7HAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EDTG,YAAY,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,mBAAmB,+BAAE,aAAa,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,QAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;4FAM/C,eAAe,EAAA,UAAA,EAAA,CAAA;kBAT3B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,YAAY,cACV,IAAI,EAAA,OAAA,EACP,CAAC,YAAY,EAAE,mBAAmB,EAAE,aAAa,CAAC,mBAG1C,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAAA,qhDAAA,EAAA,MAAA,EAAA,CAAA,y7HAAA,CAAA,EAAA;8BAG5B,KAAK,EAAA,CAAA;sBAAb;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBAGQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBAGS,SAAS,EAAA,CAAA;sBAAlB;;;ME3CU,iBAAiB,CAAA;IACnB,OAAO,GAAuC,SAAS;IACvD,IAAI,GAAiC,QAAQ;IAC7C,KAAK,GAAW,EAAE;IAClB,SAAS,GAAY,KAAK;IAC1B,aAAa,GAAY,KAAK;IAC9B,OAAO,GAAY,KAAK;AAEvB,IAAA,eAAe,GAAG,IAAI,YAAY,EAAW;IAEvD,WAAW,GAAY,KAAK;IAC5B,YAAY,GAAY,KAAK;;AAG7B,IAAA,IAAI,gBAAgB,GAAA;QAClB,OAAO;AACL,YAAA,SAAS,EAAE,IAAI,CAAC,OAAO,KAAK,SAAS;AACrC,YAAA,UAAU,EAAE,IAAI,CAAC,OAAO,KAAK,UAAU;AACvC,YAAA,OAAO,EAAE,IAAI,CAAC,IAAI,KAAK,OAAO;AAC9B,YAAA,QAAQ,EAAE,IAAI,CAAC,IAAI,KAAK,QAAQ;AAChC,YAAA,OAAO,EAAE,IAAI,CAAC,IAAI,KAAK,OAAO;YAC9B,UAAU,EAAE,IAAI,CAAC,OAAO;SACzB;;;AAIH,IAAA,IAAI,eAAe,GAAA;QACjB,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY;YAC/C,eAAe,EAAE,IAAI,CAAC,aAAa;AACnC,YAAA,UAAU,EAAE,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS;YAC9C,YAAY,EAAE,IAAI,CAAC;SACpB;;;AAIH,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY;;;AAIlE,IAAA,IAAI,aAAa,GAAA;AACf,QAAA,OAAO,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,IAAI,CAAC,aAAa;;IAGrE,cAAc,GAAA;QACZ,IAAI,IAAI,CAAC,OAAO;YAAE;AAElB,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;YAC1B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;YACzC;;AAGF,QAAA,IAAI,IAAI,CAAC,OAAO,KAAK,UAAU,EAAE;AAC/B,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,IAAI,CAAC,gBAAgB,EAAE;;iBAClB;gBACL,IAAI,CAAC,cAAc,EAAE;;;AAElB,aAAA,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;AACrC,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,IAAI,CAAC,sBAAsB,EAAE;;iBACxB;AACL,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI;gBACrB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;;;aAEtC;;AAEL,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI;gBAExB,UAAU,CAAC,MAAK;AACd,oBAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,oBAAA,IAAI,CAAC,YAAY,GAAG,KAAK;oBACzB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;AAC3C,iBAAC,EAAE,GAAG,CAAC,CAAC;;iBACH;AACL,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI;gBACrB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;;;;;AAM/C,IAAA,SAAS,CAAC,KAAoB,EAAA;;AAE5B,QAAA,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,EAAE;AAC9C,YAAA,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,cAAc,EAAE;;;IAIjB,cAAc,GAAA;AACpB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI;AACvB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;QAErB,UAAU,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK;YACxB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;AAC3C,SAAC,EAAE,GAAG,CAAC,CAAC;;IAGF,gBAAgB,GAAA;AACtB,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;QAExB,UAAU,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK;YACzB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;AAC3C,SAAC,EAAE,GAAG,CAAC,CAAC;;IAGF,sBAAsB,GAAA;AAC5B,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;QAExB,UAAU,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK;YACzB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;AAC3C,SAAC,EAAE,GAAG,CAAC,CAAC;;wGAzHC,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;4FAAjB,iBAAiB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,SAAA,EAAA,WAAA,EAAA,aAAA,EAAA,eAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECV9B,ijCAyBM,EAAA,MAAA,EAAA,CAAA,60PAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EDpBM,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAKX,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAP7B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,cAAc,WACf,CAAC,YAAY,CAAC,EAGN,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,ijCAAA,EAAA,MAAA,EAAA,CAAA,60PAAA,CAAA,EAAA;8BAGtC,OAAO,EAAA,CAAA;sBAAf;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBAES,eAAe,EAAA,CAAA;sBAAxB;;;AElBH;MAea,eAAe,CAAA;IACjB,IAAI,GAAe,QAAQ;IAC3B,KAAK,GAAW,EAAE;IAClB,QAAQ,GAAmB,MAAM;IACjC,QAAQ,GAAY,KAAK;IACzB,OAAO,GAAY,KAAK;IACxB,SAAS,GAAY,IAAI;AAExB,IAAA,aAAa,GAAG,IAAI,YAAY,EAAW;IAErD,QAAQ,GAAA;QACN,IAAI,IAAI,CAAC,QAAQ;YAAE;AAEnB,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;QAC5B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;;AAGvC,IAAA,SAAS,CAAC,KAAoB,EAAA;AAC5B,QAAA,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,EAAE;YAC9C,KAAK,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,QAAQ,EAAE;;;AAInB,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC;AACV,cAAE,CAAA,aAAA,EAAgB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,CAAE;cAC/D,IAAI;;wGA3BC,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;4FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECf5B,83CAsCM,EAAA,MAAA,EAAA,CAAA,qjHAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,ED5BM,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAKX,eAAe,EAAA,UAAA,EAAA,CAAA;kBAR3B,SAAS;+BACE,YAAY,EAAA,UAAA,EACV,IAAI,EACP,OAAA,EAAA,CAAC,YAAY,CAAC,EAAA,eAAA,EAGN,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,83CAAA,EAAA,MAAA,EAAA,CAAA,qjHAAA,CAAA,EAAA;8BAGtC,IAAI,EAAA,CAAA;sBAAZ;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBAES,aAAa,EAAA,CAAA;sBAAtB;;;MEcU,aAAa,CAAA;AAkEJ,IAAA,GAAA;IAjEX,IAAI,GAAa,EAAE;IACnB,KAAK,GAA2B,IAAI;AACnC,IAAA,WAAW,GAAG,IAAI,YAAY,EAAmB;IAClD,mBAAmB,GAAG,IAAI;AAC1B,IAAA,QAAQ;IACR,YAAY,GAAG,KAAK;AACpB,IAAA,SAAS;IACT,OAAO,GAAkC,SAAS;AAClD,IAAA,KAAK;AACL,IAAA,SAAS;IACT,SAAS,GAAG,KAAK;AACjB,IAAA,cAAc;AACb,IAAA,cAAc,GAAG,IAAI,YAAY,EAAsF;AACvH,IAAA,QAAQ,GAAG,IAAI,YAAY,EAAU;AACrC,IAAA,QAAQ,GAAG,IAAI,YAAY,EAAU;AACrC,IAAA,QAAQ,GAAG,IAAI,YAAY,EAAU;AACrC,IAAA,OAAO,GAAG,IAAI,YAAY,EAAU;AACpC,IAAA,iBAAiB,GAAG,IAAI,YAAY,EAAsF;AAC1H,IAAA,iBAAiB,GAAG,IAAI,YAAY,EAAsF;AAC1H,IAAA,iBAAiB,GAAG,IAAI,YAAY,EAAsF;AAC1H,IAAA,gBAAgB,GAAG,IAAI,YAAY,EAAsF;AACnI;;;AAGG;AACM,IAAA,WAAW,GAA6B,EAAE,OAAO,EAAE,WAAW,EAAE;AACzE;;AAEG;AACM,IAAA,SAAS;AAClB;;AAEG;AACM,IAAA,YAAY;AACrB;;AAEG;AACM,IAAA,iBAAiB;AAE1B,IAAA,IACI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;;AAGH,IAAA,OAAO;AACF,IAAA,UAAU;IAErC,SAAS,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;IACjC,iBAAiB,GAAG,KAAK;IACzB,iBAAiB,GAAG,IAAI;IACxB,kBAAkB,GAAG,KAAK;IAC1B,iBAAiB,GAAkB,IAAI;IACvC,YAAY,GAAkB,IAAI;IAClC,iBAAiB,GAAkB,IAAI;IACvC,gBAAgB,GAAyC,IAAI;AAErD,IAAA,cAAc;IACd,gBAAgB,GAAG,EAAE;IACrB,SAAS,GAA2B,IAAI;IACxC,WAAW,GAAG,KAAK;IACnB,oBAAoB,GAAyC,IAAI;IACjE,aAAa,GAAkB,EAAE;IACjC,eAAe,GAAuB,IAAI;IAC1C,kBAAkB,GAAG,KAAK;AAElC,IAAA,WAAA,CAAoB,GAAsB,EAAA;QAAtB,IAAG,CAAA,GAAA,GAAH,GAAG;;IAEvB,QAAQ,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACjG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK;;;IAIpC,eAAe,GAAA;;AAEb,QAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;YAC1B,IAAI,CAAC,mBAAmB,EAAE;AAC1B,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI;AACzB,SAAC,CAAC;;QAEF,UAAU,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC;AACpE,SAAC,CAAC;;IAGJ,kBAAkB,GAAA;;QAEhB,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC,YAAY,EAAE,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AAC/J,QAAA,IAAI,YAAY,KAAK,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;AAC3E,YAAA,IAAI,CAAC,gBAAgB,GAAG,YAAY;AACpC,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK;AAC3B,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;gBAC1B,IAAI,CAAC,mBAAmB,EAAE;AAC5B,aAAC,CAAC;;;IAIN,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,cAAc,EAAE,UAAU,EAAE;QACjC,IAAI,CAAC,2BAA2B,EAAE;;IAG5B,mBAAmB,GAAA;QACzB,IAAI,CAAC,gBAAgB,EAAE;QACvB,IAAI,CAAC,uBAAuB,EAAE;AAC9B,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,MAAK;YAC5C,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,uBAAuB,EAAE;AAChC,SAAC,CAAC;QACF,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;;IAGlD,UAAU,CAAC,GAAW,EAAE,KAAa,EAAA;QAC1C,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,CAAC;AAC3D,QAAA,IAAI,GAAG,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,GAAG,EAAE;AAClC,gBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;AAC7B,gBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;AAC5B,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,gBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;gBAC7B,IAAI,CAAC,2BAA2B,EAAE;gBAClC;;iBACK;AACL,gBAAA,IAAI,CAAC,iBAAiB,GAAG,GAAG;AAC5B,gBAAA,IAAI,KAAK,IAAI,KAAK,CAAC,aAAa,EAAE;oBAChC,MAAM,IAAI,GAAI,KAAK,CAAC,aAA6B,CAAC,qBAAqB,EAAE;oBACzE,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,WAAW;oBACpD,IAAI,CAAC,gBAAgB,GAAG;wBACtB,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;AAChC,wBAAA,GAAG,EAAE,IAAI,CAAC,MAAM,GAAG;qBACpB;;qBACI;AACL,oBAAA,IAAI,CAAC,gBAAgB,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;;AAE9C,gBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI;gBAC9B,IAAI,CAAC,wBAAwB,EAAE;gBAC/B;;;AAGJ,QAAA,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;AAC7C,YAAA,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK;YACtB,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;;AAElC,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;;AAGlB,IAAA,UAAU,CAAC,GAAW,EAAA;AAC3B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;;AAGlB,IAAA,UAAU,CAAC,GAAW,EAAA;AAC3B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;;AAGlB,IAAA,SAAS,CAAC,GAAW,EAAA;AAC1B,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;;AAGjB,IAAA,MAAM,CAAC,SAA2B,EAAA;AACvC,QAAA,MAAM,YAAY,GAAG,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG;AACtD,QAAA,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;QAC/E,UAAU,CAAC,MAAK;YACd,IAAI,CAAC,uBAAuB,EAAE;SAC/B,EAAE,GAAG,CAAC;;IAGD,gBAAgB,GAAA;QACtB,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE;AACnB,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa;QACrC,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW;QACnD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,YAAY,IAAI,WAAW;QACzD,IAAI,CAAC,uBAAuB,EAAE;AAC9B,QAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;;IAGnB,uBAAuB,GAAA;QAC5B,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE;AACnB,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa;QACrC,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC,UAAU,KAAK,CAAC;AAC5C,QAAA,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC,UAAU,GAAG,EAAE,CAAC,WAAW,IAAI,EAAE,CAAC,WAAW,GAAG,CAAC;AAC9E,QAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;;IAGlB,uBAAuB,GAAA;AAC7B,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE;QACvE,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC;QAChE,IAAI,GAAG,KAAK,CAAC,CAAC;YAAE;AAChB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,aAAa;QAC1D,IAAI,UAAU,EAAE;YACd,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,qBAAqB,EAAE;AAClE,YAAA,MAAM,OAAO,GAAG,UAAU,CAAC,qBAAqB,EAAE;YAClD,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK;YACpC,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU;AACzF,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;;;AAI5B,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC;;IAGjD,mBAAmB,CAAC,GAAW,EAAE,IAA8D,EAAA;AACpG,QAAA,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK;QACtB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;AAChC,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;AAC/C,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;AAClD,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;;IAGxB,mBAAmB,CAAC,GAAW,EAAE,IAA8D,EAAA;AACpG,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;;IAG7C,mBAAmB,CAAC,GAAW,EAAE,IAA8D,EAAA;AACpG,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;;IAG7C,kBAAkB,CAAC,GAAW,EAAE,IAA8D,EAAA;AACnG,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;;IAG5C,kBAAkB,CAAC,CAAS,EAAE,SAAuB,EAAA;AAC1D,QAAA,IAAI,CAAC,YAAY,GAAG,CAAC;AACrB,QAAA,IAAI,CAAC,iBAAiB,GAAG,CAAC;AAC1B,QAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,YAAA,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC;AACvC,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;;QAElC,IAAI,SAAS,EAAE;AACb,YAAA,MAAM,IAAI,GAAG,SAAS,CAAC,qBAAqB,EAAE;YAC9C,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,WAAW;YACpD,IAAI,CAAC,gBAAgB,GAAG;gBACtB,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;AAChC,gBAAA,GAAG,EAAE,IAAI,CAAC,MAAM,GAAG;aACpB;;QAEH,IAAI,CAAC,wBAAwB,EAAE;;AAG1B,IAAA,kBAAkB,CAAC,CAAS,EAAA;AACjC,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,QAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,YAAA,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC;AACvC,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;;AAElC,QAAA,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC,MAAK;AAC1C,YAAA,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE;AAC3D,gBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;AAC7B,gBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;;SAE/B,EAAE,GAAG,CAAC;;IAGF,mBAAmB,CAAC,CAAS,EAAE,GAAiB,EAAA;AACrD,QAAA,IAAI,CAAC,iBAAiB,GAAG,CAAC;AAC1B,QAAA,IAAI,CAAC,iBAAiB,GAAG,CAAC;AAC1B,QAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,YAAA,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC;AACvC,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;;QAElC,IAAI,GAAG,EAAE;AACP,YAAA,IAAI,CAAC,eAAe,GAAG,GAAG;;;AAIvB,IAAA,mBAAmB,CAAC,CAAS,EAAA;AAClC,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;AAC7B,QAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,YAAA,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC;AACvC,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;;AAElC,QAAA,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC,MAAK;AAC1C,YAAA,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE;AAC3D,gBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;AAC7B,gBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;;SAE/B,EAAE,GAAG,CAAC;;IAGD,wBAAwB,GAAA;QAC9B,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC;;IAGhE,2BAA2B,GAAA;QACjC,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC;;AAGnE,IAAA,mBAAmB,GAAG,CAAC,KAAiB,KAAI;AAClD,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC3B,YAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK;YAC/B;;QAEF,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI;YAAE;AAC9D,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAkB,CAAC;AAC7D,QAAA,IACE,YAAY;AACZ,YAAA,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAc,CAAC;YAC5C,SAAS;YACT,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAc,CAAC,EACzC;AACA,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;AAC7B,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;AAC5B,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;YAC7B,IAAI,CAAC,2BAA2B,EAAE;;AAEtC,KAAC;AAED,IAAA,IAAI,WAAW,GAAA;QACb,OAAO;AACL,YAAA,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE;SACvE;;wGA3TQ,aAAa,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;4FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAAA,WAAA,EAAA,OAAA,EAAA,SAAA,EAAA,KAAA,EAAA,OAAA,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,EAAA,WAAA,EAAA,YAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,YAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECrC1B,2khBA2PM,ED5NM,MAAA,EAAA,CAAA,0xOAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,8rBAAE,mBAAmB,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,aAAa,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,QAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,eAAe,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,OAAA,EAAA,QAAA,EAAA,MAAA,EAAA,UAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,UAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,cAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;4FAMhE,aAAa,EAAA,UAAA,EAAA,CAAA;kBATzB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,UAAU,cACR,IAAI,EAAA,OAAA,EACP,CAAC,YAAY,EAAE,mBAAmB,EAAE,aAAa,EAAE,eAAe,CAAC,mBAG3D,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAAA,2khBAAA,EAAA,MAAA,EAAA,CAAA,0xOAAA,CAAA,EAAA;sFAG5B,IAAI,EAAA,CAAA;sBAAZ;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACS,WAAW,EAAA,CAAA;sBAApB;gBACQ,mBAAmB,EAAA,CAAA;sBAA3B;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACS,cAAc,EAAA,CAAA;sBAAvB;gBACS,QAAQ,EAAA,CAAA;sBAAjB;gBACS,QAAQ,EAAA,CAAA;sBAAjB;gBACS,QAAQ,EAAA,CAAA;sBAAjB;gBACS,OAAO,EAAA,CAAA;sBAAhB;gBACS,iBAAiB,EAAA,CAAA;sBAA1B;gBACS,iBAAiB,EAAA,CAAA;sBAA1B;gBACS,iBAAiB,EAAA,CAAA;sBAA1B;gBACS,gBAAgB,EAAA,CAAA;sBAAzB;gBAKQ,WAAW,EAAA,CAAA;sBAAnB;gBAIQ,SAAS,EAAA,CAAA;sBAAjB;gBAIQ,YAAY,EAAA,CAAA;sBAApB;gBAIQ,iBAAiB,EAAA,CAAA;sBAAzB;gBAGG,SAAS,EAAA,CAAA;sBADZ,WAAW;uBAAC,oBAAoB;gBAKX,OAAO,EAAA,CAAA;sBAA5B,SAAS;uBAAC,SAAS;gBACO,UAAU,EAAA,CAAA;sBAApC,YAAY;uBAAC,WAAW;;;MElEd,2BAA2B,CAAA;IAC7B,WAAW,GAAG,CAAC;IACf,UAAU,GAAG,EAAE;IACf,IAAI,GAKU,OAAO;AAEpB,IAAA,UAAU,GAAG,IAAI,YAAY,EAAU;AAEjD,IAAA,QAAQ,CAAC,IAAqB,EAAA;QAC5B,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE;AACzD,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;;;;AAK9B,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,QAAQ,IAAI,CAAC,IAAI;AACf,YAAA,KAAK,OAAO;AACV,gBAAA,OAAO,IAAI,CAAC,aAAa,EAAE;AAC7B,YAAA,KAAK,UAAU;AACb,gBAAA,OAAO,IAAI,CAAC,gBAAgB,EAAE;AAChC,YAAA,KAAK,UAAU;AACb,gBAAA,OAAO,IAAI,CAAC,gBAAgB,EAAE;AAChC,YAAA;AACE,gBAAA,OAAO,EAAE;;;IAIP,aAAa,GAAA;QACnB,MAAM,KAAK,GAAwB,EAAE;AACrC,QAAA,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,IAAI;AAExC,QAAA,IAAI,UAAU,IAAI,CAAC,EAAE;YACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,EAAE;AAAE,gBAAA,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;;aAC9C;AACL,YAAA,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;YACb,IAAI,WAAW,GAAG,CAAC;AAAE,gBAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AACtC,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC;AAC1C,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC;YACrD,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE;AAAE,gBAAA,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAChD,YAAA,IAAI,WAAW,GAAG,UAAU,GAAG,CAAC;AAAE,gBAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AACnD,YAAA,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;;AAGxB,QAAA,OAAO,KAAK;;IAGN,gBAAgB,GAAA;QACtB,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;IAGzD,gBAAgB,GAAA;AACtB,QAAA,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,IAAI;QACxC,MAAM,KAAK,GAAwB,EAAE;QAErC,MAAM,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5B,QAAA,MAAM,SAAS,GAAG,CAAC,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,UAAU,CAAC;;AAG9D,QAAA,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI;YACvB,IAAI,CAAC,IAAI,UAAU;AAAE,gBAAA,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AACpC,SAAC,CAAC;;AAGF,QAAA,IAAI,WAAW,IAAI,CAAC,EAAE;AACpB,YAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AACjB,YAAA,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI;gBACtB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AAAE,oBAAA,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAChD,aAAC,CAAC;AACF,YAAA,OAAO,KAAK;;;AAId,QAAA,IAAI,WAAW,KAAK,CAAC,EAAE;AACrB,YAAA,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AACb,YAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AACjB,YAAA,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI;AACtB,gBAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;AAAE,oBAAA,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AACvC,aAAC,CAAC;AACF,YAAA,OAAO,KAAK;;;QAId,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,UAAU,GAAG,CAAC,EAAE;AACnD,YAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AACjB,YAAA,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC;AACvB,YAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AACjB,YAAA,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI;AACtB,gBAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;AAAE,oBAAA,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AACvC,aAAC,CAAC;AACF,YAAA,OAAO,KAAK;;;AAId,QAAA,IAAI,WAAW,IAAI,UAAU,GAAG,CAAC,EAAE;AACjC,YAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AACjB,YAAA,KAAK,IAAI,CAAC,GAAG,UAAU,GAAG,CAAC,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,EAAE,EAAE;gBACjD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AAAE,oBAAA,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;;AAEhD,YAAA,OAAO,KAAK;;AAGd,QAAA,OAAO,KAAK;;AAGd,IAAA,UAAU,CAAC,IAAqB,EAAA;AAC9B,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,YAAA,OAAO,KAAK;;AAGd,QAAA,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,IAAI;QACxC,MAAM,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B,MAAM,SAAS,GAAG,CAAC,UAAU,GAAG,CAAC,EAAE,UAAU,CAAC;AAE9C,QAAA,QACE,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC;AACzB,YAAA,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxB,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC;;AAIrC,IAAA,gBAAgB,CAAC,KAAa,EAAA;AAC5B,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;AACxB,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAE7B,QAAA,QACE,OAAO,IAAI,KAAK,QAAQ;YACxB,OAAO,IAAI,KAAK,QAAQ;AACxB,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACrB,YAAA,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;;IAI1B,QAAQ,GAAA;QACN,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,EAAE;YACtC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;;;IAI9C,QAAQ,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;;;wGAlJnC,2BAA2B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;4FAA3B,2BAA2B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,yBAAA,EAAA,MAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,UAAA,EAAA,YAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECjBxC,quIAuJA,EAAA,MAAA,EAAA,CAAA,g6FAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,ED3IY,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAKX,2BAA2B,EAAA,UAAA,EAAA,CAAA;kBARvC,SAAS;+BACE,yBAAyB,EAAA,UAAA,EACvB,IAAI,EACP,OAAA,EAAA,CAAC,YAAY,CAAC,EAAA,eAAA,EAGN,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,quIAAA,EAAA,MAAA,EAAA,CAAA,g6FAAA,CAAA,EAAA;8BAGtC,WAAW,EAAA,CAAA;sBAAnB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBAOS,UAAU,EAAA,CAAA;sBAAnB;;;MEfU,kBAAkB,CAAA;IACpB,QAAQ,GAAG,KAAK;IAChB,SAAS,GAAG,KAAK;IACjB,UAAU,GAAG,KAAK;IAClB,UAAU,GAAG,EAAE;IACf,QAAQ,GAAG,EAAE;IACb,SAAS,GAAG,EAAE;IACd,YAAY,GAAqB,MAAM;IACvC,IAAI,GAA4B,SAAS;AAClD,IAAA,IAAI,gBAAgB,GAAA;QAClB,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB;;AAGH,IAAA,kBAAkB,CAAC,KAAoB,EAAA;AACrC,QAAA,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;YAC9C,IAAI,CAAC,YAAY,EAAE;YACnB,KAAK,CAAC,cAAc,EAAE;;;IAI1B,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ;;;wGAzBvB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,kBAAkB,ECZ/B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,YAAA,EAAA,cAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,8mEAoEA,ED9DY,MAAA,EAAA,CAAA,w9EAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,gOAAC,mBAAmB,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,sBAAA,EAAA,QAAA,EAAA,oDAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAM/B,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAR9B,SAAS;+BACE,eAAe,EAAA,OAAA,EAChB,CAAC,YAAY,EAAC,mBAAmB,CAAC,EAAA,UAAA,EAC/B,IAAI,EAAA,eAAA,EAGC,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,8mEAAA,EAAA,MAAA,EAAA,CAAA,w9EAAA,CAAA,EAAA;8BAGtC,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,IAAI,EAAA,CAAA;sBAAZ;;;MEiCU,mBAAmB,CAAA;AAwDV,IAAA,GAAA;IAvDX,KAAK,GAAG,EAAE;IACV,WAAW,GAAG,EAAE;IAChB,OAAO,GAAmB,SAAS;IACnC,IAAI,GAAgB,IAAI;IACxB,QAAQ,GAAG,KAAK;IAChB,QAAQ,GAAG,KAAK;IAChB,KAAK,GAAG,EAAE;IACV,MAAM,GAAG,EAAE;IACX,IAAI,GAAG,EAAE;IACT,YAAY,GAAiB,OAAO;IACpC,SAAS,GAAG,2BAA2B;IACvC,EAAE,GAAG,EAAE;IACP,IAAI,GAAG,EAAE;IACT,YAAY,GAAG,EAAE;IACjB,IAAI,GAAG,MAAM;AACb,IAAA,SAAS;AACT,IAAA,SAAS;IACT,QAAQ,GAAG,KAAK;IAChB,SAAS,GAAG,KAAK;AACjB,IAAA,KAAK;;IAGL,WAAW,CAAoB;IAC/B,cAAc,CAAqB;IACnC,cAAc,CAAqB;IACnC,eAAe,CAAqB;IACpC,iBAAiB,CAAqB;AACtC,IAAA,eAAe,GAAG,IAAI,CAAC;AACvB,IAAA,yBAAyB,GAAG,IAAI,CAAC;;IAGjC,QAAQ,GAAsB,EAAE;AAE/B,IAAA,WAAW,GAAG,IAAI,YAAY,EAAS;AACvC,IAAA,YAAY,GAAG,IAAI,YAAY,EAAS;AACxC,IAAA,YAAY,GAAG,IAAI,YAAY,EAAS;AACxC,IAAA,aAAa,GAAG,IAAI,YAAY,EAAS;AACzC,IAAA,cAAc,GAAG,IAAI,YAAY,EAAS;AAC1C,IAAA,YAAY,GAAG,IAAI,YAAY,EAAS;AAEF,IAAA,eAAe;AACf,IAAA,eAAe;AACZ,IAAA,kBAAkB;AACpB,IAAA,gBAAgB;IAEjE,KAAK,GAAG,EAAE;IACV,SAAS,GAAG,KAAK;IACjB,kBAAkB,GAAG,KAAK;IAC1B,kBAAkB,GAAG,KAAK;IAC1B,qBAAqB,GAAG,KAAK;IAC7B,mBAAmB,GAAG,KAAK;AAEnB,IAAA,QAAQ,GAA4B,MAAK,GAAe;AACxD,IAAA,SAAS,GAAe,MAAK,GAAe;AAEpD,IAAA,WAAA,CAAoB,GAAsB,EAAA;QAAtB,IAAG,CAAA,GAAA,GAAH,GAAG;;IAEvB,eAAe,GAAA;;QAEb,IAAI,CAAC,qBAAqB,EAAE;;IAGtB,qBAAqB,GAAA;;QAE3B,UAAU,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,eAAe,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,GAAG,CAAC;AACnF,YAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,eAAe,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,GAAG,CAAC;AACnF,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,kBAAkB,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,GAAG,CAAC;AACzF,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,gBAAgB,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,GAAG,CAAC;AACrF,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,SAAC,CAAC;;;AAIJ,IAAA,UAAU,CAAC,KAAa,EAAA;AACtB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,EAAE;AACxB,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGzB,IAAA,gBAAgB,CAAC,EAA2B,EAAA;AAC1C,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;AAGpB,IAAA,iBAAiB,CAAC,EAAc,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;AAGrB,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU;AAC1B,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;;AAIzB,IAAA,OAAO,CAAC,KAAY,EAAA;AAClB,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAA0B;AAC/C,QAAA,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;AACzB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;AACzB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;;AAG/B,IAAA,OAAO,CAAC,KAAY,EAAA;AAClB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;;AAG/B,IAAA,MAAM,CAAC,KAAY,EAAA;AACjB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC,SAAS,EAAE;AAChB,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;;AAG9B,IAAA,SAAS,CAAC,KAAY,EAAA;AACpB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;;;AAIhC,IAAA,gBAAgB,CAAC,KAAY,EAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;YAAE;QACpC,KAAK,CAAC,eAAe,EAAE;AACvB,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;;AAGjC,IAAA,cAAc,CAAC,KAAY,EAAA;AACzB,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;YAAE;QACpC,KAAK,CAAC,eAAe,EAAE;AACvB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;;;IAI/B,aAAa,CAAC,KAAoB,EAAE,QAAyB,EAAA;AAC3D,QAAA,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;YAC9C,KAAK,CAAC,cAAc,EAAE;YACtB,IAAI,QAAQ,KAAK,OAAO;AAAE,gBAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;;AACjD,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;;;;AAKnC,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK;;AAGrB,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ;;AAGxC,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI;;AAKpB,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,EAAE,IAAI,eAAe,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA,CAAE;;AAG5E,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,CAAG,EAAA,IAAI,CAAC,OAAO,QAAQ;;AAGhC,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,CAAG,EAAA,IAAI,CAAC,OAAO,SAAS;;AAGjC,IAAA,IAAI,eAAe,GAAA;QACjB,MAAM,GAAG,GAAa,EAAE;QACxB,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;QACzC,IAAI,IAAI,CAAC,SAAS;AAAE,YAAA,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC3C,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;;AAG5B;;AAEG;AACH,IAAA,IAAI,eAAe,GAAA;QACjB,IAAI,CAAC,IAAI,CAAC,eAAe;AAAE,YAAA,OAAO,EAAE;QAEpC,MAAM,OAAO,GAAa,EAAE;;AAG5B,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,CAAC,IAAI,CAAC,CAAA,yBAAA,EAA4B,IAAI,CAAC,WAAW,CAAE,CAAA,CAAC;;;AAI9D,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;YACrC,OAAO,CAAC,IAAI,CAAC,CAAA,mBAAA,EAAsB,IAAI,CAAC,cAAc,CAAE,CAAA,CAAC;;AAE3D,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;YACrC,OAAO,CAAC,IAAI,CAAC,CAAA,mBAAA,EAAsB,IAAI,CAAC,cAAc,CAAE,CAAA,CAAC;;AAE3D,QAAA,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE;YACtC,OAAO,CAAC,IAAI,CAAC,CAAA,oBAAA,EAAuB,IAAI,CAAC,eAAe,CAAE,CAAA,CAAC;;AAE7D,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,EAAE;YACxC,OAAO,CAAC,IAAI,CAAC,CAAA,sBAAA,EAAyB,IAAI,CAAC,iBAAiB,CAAE,CAAA,CAAC;;AAGjE,QAAA,OAAO,OAAO;;AAGhB;;AAEG;AACH,IAAA,IAAI,qBAAqB,GAAA;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ;AAAE,YAAA,OAAO,EAAE;QAE7B,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AAChC,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAA,aAAA,EAAgB,CAAC,CAAA,CAAE,CAAC;;aAC7C;AACL,YAAA,OAAO,CAAC,CAAgB,aAAA,EAAA,IAAI,CAAC,QAAQ,CAAA,CAAE,CAAC;;;AAI5C,IAAA,IAAI,YAAY,GAAA;AACd,QAAA,MAAM,OAAO,GAAG,CAAC,oBAAoB,CAAC;QAEtC,IAAI,IAAI,CAAC,IAAI;YAAE,OAAO,CAAC,IAAI,CAAC,CAAA,oBAAA,EAAuB,IAAI,CAAC,IAAI,CAAE,CAAA,CAAC;QAC/D,IAAI,IAAI,CAAC,OAAO;YAAE,OAAO,CAAC,IAAI,CAAC,CAAA,oBAAA,EAAuB,IAAI,CAAC,OAAO,CAAE,CAAA,CAAC;QACrE,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC;QAC5D,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC;QAC/D,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC;QAC/D,IAAI,IAAI,CAAC,SAAS;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC;QAC/D,IAAI,IAAI,CAAC,SAAS;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC;;QAGlE,IAAI,IAAI,CAAC,qBAAqB;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC;QAC9E,IAAI,IAAI,CAAC,mBAAmB;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC;QAC1E,IAAI,IAAI,CAAC,kBAAkB;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC;QAC5E,IAAI,IAAI,CAAC,kBAAkB;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC;AAE5E,QAAA,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;;AAG1B,IAAA,IAAI,cAAc,GAAA;AAChB,QAAA,MAAM,OAAO,GAAG,CAAC,aAAa,CAAC;QAE/B,IAAI,IAAI,CAAC,IAAI;YAAE,OAAO,CAAC,IAAI,CAAC,CAAA,aAAA,EAAgB,IAAI,CAAC,IAAI,CAAE,CAAA,CAAC;QACxD,IAAI,IAAI,CAAC,OAAO;YAAE,OAAO,CAAC,IAAI,CAAC,CAAA,aAAA,EAAgB,IAAI,CAAC,OAAO,CAAE,CAAA,CAAC;QAC9D,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC;QACrD,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC;QACxD,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC;QACxD,IAAI,IAAI,CAAC,SAAS;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC;QACxD,IAAI,IAAI,CAAC,SAAS;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC;;AAG3D,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe;AAC5C,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB;AAEhD,QAAA,OAAO,CAAC,GAAG,OAAO,EAAE,GAAG,eAAe,EAAE,GAAG,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;;wGA1P1D,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,mBAAmB,EApBnB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,WAAA,EAAA,aAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,YAAA,EAAA,cAAA,EAAA,IAAA,EAAA,MAAA,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,KAAA,EAAA,OAAA,EAAA,WAAA,EAAA,aAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,yBAAA,EAAA,2BAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,YAAA,EAAA,cAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,eAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,iBAAiB;AAC1B,gBAAA,WAAW,EAAE,UAAU,CAAC,MAAM,mBAAmB,CAAC;AAClD,gBAAA,KAAK,EAAE,IAAI;AACZ,aAAA;AACF,SAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECvCH,4zFA6FA,EAAA,MAAA,EAAA,CAAA,goYAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EDhEY,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,aAAa,EAYzB,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,QAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA;YACV,OAAO,CAAC,UAAU,EAAE;gBAClB,UAAU,CAAC,QAAQ,EAAE;oBACnB,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC;AAC9C,oBAAA,OAAO,CAAC,iCAAiC,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;iBACxF,CAAC;gBACF,UAAU,CAAC,QAAQ,EAAE;AACnB,oBAAA,OAAO,CAAC,iCAAiC,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC;iBAC1F;aACF;AACF,SAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAEU,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBA3B/B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,aAAa,EACX,UAAA,EAAA,IAAI,EACP,OAAA,EAAA,CAAC,YAAY,EAAE,aAAa,CAAC,EAGrB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACpC,SAAA,EAAA;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAE,UAAU,CAAC,yBAAyB,CAAC;AAClD,4BAAA,KAAK,EAAE,IAAI;AACZ,yBAAA;qBACF,EACQ,OAAA,EAAA,CAAC,sBAAsB,CAAC,EACrB,UAAA,EAAA;wBACV,OAAO,CAAC,UAAU,EAAE;4BAClB,UAAU,CAAC,QAAQ,EAAE;gCACnB,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC;AAC9C,gCAAA,OAAO,CAAC,iCAAiC,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;6BACxF,CAAC;4BACF,UAAU,CAAC,QAAQ,EAAE;AACnB,gCAAA,OAAO,CAAC,iCAAiC,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC;6BAC1F;yBACF;AACF,qBAAA,EAAA,QAAA,EAAA,4zFAAA,EAAA,MAAA,EAAA,CAAA,goYAAA,CAAA,EAAA;sFAGQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,EAAE,EAAA,CAAA;sBAAV;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBAGQ,WAAW,EAAA,CAAA;sBAAnB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACQ,iBAAiB,EAAA,CAAA;sBAAzB;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACQ,yBAAyB,EAAA,CAAA;sBAAjC;gBAGQ,QAAQ,EAAA,CAAA;sBAAhB;gBAES,WAAW,EAAA,CAAA;sBAApB;gBACS,YAAY,EAAA,CAAA;sBAArB;gBACS,YAAY,EAAA,CAAA;sBAArB;gBACS,aAAa,EAAA,CAAA;sBAAtB;gBACS,cAAc,EAAA,CAAA;sBAAvB;gBACS,YAAY,EAAA,CAAA;sBAArB;gBAE+C,eAAe,EAAA,CAAA;sBAA9D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,iBAAiB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACE,eAAe,EAAA,CAAA;sBAA9D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,iBAAiB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACK,kBAAkB,EAAA,CAAA;sBAApE,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,oBAAoB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACA,gBAAgB,EAAA,CAAA;sBAAhE,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,kBAAkB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;;;MEhEpC,oBAAoB,CAAA;AA6BX,IAAA,GAAA;IA5BX,KAAK,GAAG,EAAE;IACV,WAAW,GAAG,EAAE;IAChB,OAAO,GAAoB,SAAS;IACpC,IAAI,GAAiB,IAAI;IACzB,QAAQ,GAAG,KAAK;IAChB,QAAQ,GAAG,KAAK;IAChB,KAAK,GAAG,EAAE;IACV,MAAM,GAAG,EAAE;IACX,IAAI,GAAG,CAAC;IACR,EAAE,GAAG,EAAE;IACP,IAAI,GAAG,EAAE;AACT,IAAA,SAAS;AACT,IAAA,SAAS;IACT,QAAQ,GAAG,KAAK;IAChB,SAAS,GAAG,KAAK;AACjB,IAAA,KAAK;IACL,SAAS,GAAG,IAAI;AAEf,IAAA,YAAY,GAAG,IAAI,YAAY,EAAS;AACxC,IAAA,aAAa,GAAG,IAAI,YAAY,EAAS;AACzC,IAAA,aAAa,GAAG,IAAI,YAAY,EAAS;AACzC,IAAA,cAAc,GAAG,IAAI,YAAY,EAAS;AAC1C,IAAA,cAAc,GAAG,IAAI,YAAY,EAAS;AAC1C,IAAA,YAAY,GAAG,IAAI,YAAY,EAAS;IAElD,KAAK,GAAG,EAAE;IACV,SAAS,GAAG,KAAK;AAEjB,IAAA,WAAA,CAAoB,GAAsB,EAAA;QAAtB,IAAG,CAAA,GAAA,GAAH,GAAG;;;AAGvB,IAAA,UAAU,CAAC,KAAa,EAAA;AACtB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,EAAE;AACxB,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;;;AAIzB,IAAA,QAAQ,GAA4B,MAAK,GAAG;;AAE5C,IAAA,SAAS,GAAe,MAAK,GAAG;IAChC,gBAAgB,CAAC,EAA2B,EAAA,EAAU,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACzE,iBAAiB,CAAC,EAAc,EAAA,EAAU,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AAC9D,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU;AAC1B,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;;AAIzB,IAAA,OAAO,CAAC,KAAY,EAAA;AAClB,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAA6B;AAClD,QAAA,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;AACzB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;AACzB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;;AAEhC,IAAA,OAAO,CAAC,KAAY,EAAA;AAClB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;;AAEhC,IAAA,MAAM,CAAC,KAAY,EAAA;AACjB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC,SAAS,EAAE;AAChB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;;AAE/B,IAAA,SAAS,CAAC,KAAY,EAAA;AACpB,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;;;AAIjC,IAAA,gBAAgB,CAAC,KAAY,EAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;YAAE;QACpC,KAAK,CAAC,eAAe,EAAE;AACvB,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;;AAEjC,IAAA,cAAc,CAAC,KAAY,EAAA;AACzB,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;YAAE;QACpC,KAAK,CAAC,eAAe,EAAE;AACvB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;;IAE/B,aAAa,CAAC,KAAoB,EAAE,QAAyB,EAAA;AAC3D,QAAA,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;YAC9C,KAAK,CAAC,cAAc,EAAE;YACtB,IAAI,QAAQ,KAAK,OAAO;AAAE,gBAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;;AACjD,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;;;;AAKnC,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK;;AAErB,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM;;AAEtB,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,EAAE,IAAI,gBAAgB,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA,CAAE;;AAE7E,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,CAAG,EAAA,IAAI,CAAC,OAAO,QAAQ;;AAEhC,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,CAAG,EAAA,IAAI,CAAC,OAAO,SAAS;;AAEjC,IAAA,IAAI,eAAe,GAAA;QACjB,MAAM,GAAG,GAAa,EAAE;QACxB,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;QACzC,IAAI,IAAI,CAAC,SAAS;AAAE,YAAA,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC3C,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;;AAE5B,IAAA,IAAI,YAAY,GAAA;AACd,QAAA,MAAM,OAAO,GAAG,CAAC,qBAAqB,CAAC;QACvC,IAAI,IAAI,CAAC,IAAI;YAAE,OAAO,CAAC,IAAI,CAAC,CAAA,qBAAA,EAAwB,IAAI,CAAC,IAAI,CAAE,CAAA,CAAC;QAChE,IAAI,IAAI,CAAC,OAAO;YAAE,OAAO,CAAC,IAAI,CAAC,CAAA,qBAAA,EAAwB,IAAI,CAAC,OAAO,CAAE,CAAA,CAAC;QACtE,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC;QAC7D,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC;QAChE,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC;QAChE,IAAI,IAAI,CAAC,SAAS;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC;QAChE,IAAI,IAAI,CAAC,SAAS;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC;AACnE,QAAA,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;;AAE1B,IAAA,IAAI,cAAc,GAAA;AAChB,QAAA,MAAM,OAAO,GAAG,CAAC,cAAc,CAAC;QAChC,IAAI,IAAI,CAAC,IAAI;YAAE,OAAO,CAAC,IAAI,CAAC,CAAA,cAAA,EAAiB,IAAI,CAAC,IAAI,CAAE,CAAA,CAAC;QACzD,IAAI,IAAI,CAAC,OAAO;YAAE,OAAO,CAAC,IAAI,CAAC,CAAA,cAAA,EAAiB,IAAI,CAAC,OAAO,CAAE,CAAA,CAAC;QAC/D,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC;QACtD,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC;QACzD,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC;QACzD,IAAI,IAAI,CAAC,SAAS;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC;QACzD,IAAI,IAAI,CAAC,SAAS;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC;AAC5D,QAAA,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;;wGAjIf,oBAAoB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAApB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,oBAAoB,EATpB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,WAAA,EAAA,aAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,KAAA,EAAA,OAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,eAAA,EAAA,aAAA,EAAA,eAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,iBAAiB;AAC1B,gBAAA,WAAW,EAAE,UAAU,CAAC,MAAM,oBAAoB,CAAC;AACnD,gBAAA,KAAK,EAAE,IAAI;AACZ,aAAA;AACF,SAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC9BH,uhFAgFA,EAAA,MAAA,EAAA,CAAA,u6IAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,ED5DY,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,aAAa,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,QAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAa1B,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAhBhC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,cAAc,EACZ,UAAA,EAAA,IAAI,EACP,OAAA,EAAA,CAAC,YAAY,EAAE,aAAa,CAAC,EAGrB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACpC,SAAA,EAAA;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAE,UAAU,CAAC,0BAA0B,CAAC;AACnD,4BAAA,KAAK,EAAE,IAAI;AACZ,yBAAA;qBACF,EACQ,OAAA,EAAA,CAAC,sBAAsB,CAAC,EAAA,QAAA,EAAA,uhFAAA,EAAA,MAAA,EAAA,CAAA,u6IAAA,CAAA,EAAA;sFAGxB,KAAK,EAAA,CAAA;sBAAb;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,EAAE,EAAA,CAAA;sBAAV;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBAES,YAAY,EAAA,CAAA;sBAArB;gBACS,aAAa,EAAA,CAAA;sBAAtB;gBACS,aAAa,EAAA,CAAA;sBAAtB;gBACS,cAAc,EAAA,CAAA;sBAAvB;gBACS,cAAc,EAAA,CAAA;sBAAvB;gBACS,YAAY,EAAA,CAAA;sBAArB;;;ME3CU,eAAe,CAAA;IACjB,KAAK,GAAe,SAAS;IAC7B,IAAI,GAAc,QAAQ;AAC1B,IAAA,KAAK;AACL,IAAA,QAAQ;AACR,IAAA,SAAS;AACT,IAAA,QAAQ;AAEjB,IAAA,IAAI,YAAY,GAAA;QACd,IAAI,CAAC,IAAI,CAAC,KAAK;AAAE,YAAA,OAAO,EAAE;AAC1B,QAAA,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG;AAAE,YAAA,OAAO,MAAM;AACnC,QAAA,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE;AAAE,YAAA,OAAO,KAAK;AACjC,QAAA,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC;AAAE,YAAA,OAAO,IAAI;AAC/B,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;;AAG9B,IAAA,IAAI,YAAY,GAAA;QACd,MAAM,WAAW,GAAG,CAAA,aAAA,EAAgB,IAAI,CAAC,KAAK,CAAA,QAAA,EAAW,IAAI,CAAC,IAAI,CAAA,CAAE;;AAEpE,QAAA,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9C,OAAO,CAAA,EAAG,WAAW,CAAA,gBAAA,CAAkB;;AAEzC,QAAA,OAAO,WAAW;;AAGpB,IAAA,IAAI,UAAU,GAAA;QACZ,OAAO,CAAC,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC;;AAGxC,IAAA,IAAI,aAAa,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC;;IAGvE,UAAU,GAAA;;AAER,QAAA,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC;;;wGAnC1C,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,eAAe,ECd5B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,8pBAqBA,EDZY,MAAA,EAAA,CAAA,qsDAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,mIAAE,aAAa,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,QAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAK1B,eAAe,EAAA,UAAA,EAAA,CAAA;kBAP3B,SAAS;+BACE,YAAY,EAAA,OAAA,EACb,CAAC,YAAY,EAAE,aAAa,CAAC,EAAA,eAAA,EAGrB,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,8pBAAA,EAAA,MAAA,EAAA,CAAA,qsDAAA,CAAA,EAAA;8BAGtC,KAAK,EAAA,CAAA;sBAAb;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;;;MELU,gBAAgB,CAAA;IACpB,IAAI,GAAe,OAAO;IAC1B,KAAK,GAAgB,MAAM;IAC3B,QAAQ,GAAW,EAAE;AACrB,IAAA,UAAU;AACV,IAAA,WAAW;AACX,IAAA,UAAU;AACV,IAAA,SAAS;AACT,IAAA,UAAU;AAEnB,IAAA,IAAI,aAAa,GAAA;QACjB,OAAO,CAAA,eAAA,EAAkB,IAAI,CAAC,IAAI,YAAY,IAAI,CAAC,KAAK,CAAA,CAAE;;AAG1D,IAAA,IAAI,QAAQ,GAAA;QACZ,OAAO,CAAC,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC;;AAG7C,IAAA,IAAI,aAAa,GAAA;AACjB,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU;;AAGxB,IAAA,IAAI,cAAc,GAAA;AAClB,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW;;AAGzB,IAAA,IAAI,UAAU,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,cAAc;;wGA3BnC,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,gBAAgB,ECf7B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,aAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,i2BA0BA,EDhBU,MAAA,EAAA,CAAA,iiDAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,mIAAE,eAAe,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,MAAA,EAAA,OAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAK1B,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAP5B,SAAS;+BACA,aAAa,EAAA,OAAA,EACd,CAAC,YAAY,EAAE,eAAe,CAAC,EAAA,eAAA,EAGvB,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,i2BAAA,EAAA,MAAA,EAAA,CAAA,iiDAAA,CAAA,EAAA;8BAGtC,IAAI,EAAA,CAAA;sBAAZ;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;;;MEVY,gBAAgB,CAAA;IAClB,IAAI,GAAgB,UAAU;IAC9B,IAAI,GAAgB,IAAI;IACxB,SAAS,GAAW,EAAE;IACtB,SAAS,GAAW,IAAI;IACxB,KAAK,GAAwE,SAAS;AACtF,IAAA,aAAa;;AAGtB,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,MAAM,OAAO,GAAgC;AAC3C,YAAA,EAAE,EAAE,SAAS;AACb,YAAA,EAAE,EAAE,SAAS;AACb,YAAA,EAAE,EAAE,SAAS;AACb,YAAA,EAAE,EAAE;SACL;AACD,QAAA,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;;;AAI3B,IAAA,IAAI,iBAAiB,GAAA;AACnB,QAAA,OAAO,CAAC,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;;AAGvE,IAAA,IAAI,aAAa,GAAA;AACjB,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS;AAAE,YAAA,OAAO,EAAE;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;QAE9C,IAAI,OAAO,IAAI,EAAE;AAAE,YAAA,OAAO,WAAW;QACrC,IAAI,OAAO,IAAI,EAAE;AAAE,YAAA,OAAO,WAAW;QACrC,IAAI,OAAO,IAAI,EAAE;AAAE,YAAA,OAAO,WAAW;AACrC,QAAA,OAAO,YAAY;;wGA/BR,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;4FAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,MAAA,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,WAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECb7B,46BAuBM,EAAA,MAAA,EAAA,CAAA,2wEAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EDdM,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,CAAA,EAAA,CAAA;;4FAIX,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAP5B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,aAAa,EACX,UAAA,EAAA,IAAI,EACP,OAAA,EAAA,CAAC,YAAY,CAAC,EAAA,QAAA,EAAA,46BAAA,EAAA,MAAA,EAAA,CAAA,2wEAAA,CAAA,EAAA;8BAKd,IAAI,EAAA,CAAA;sBAAZ;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,aAAa,EAAA,CAAA;sBAArB;;;MEVU,aAAa,CAAA;IAEf,OAAO,GAAG,EAAE;IACZ,OAAO,GAAG,EAAE;wGAHV,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;4FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECT1B,0pBAiBM,EAAA,MAAA,EAAA,CAAA,glBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EDbM,YAAY,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAKX,aAAa,EAAA,UAAA,EAAA,CAAA;kBAPzB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,UAAU,WACX,CAAC,YAAY,CAAC,EAGN,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,0pBAAA,EAAA,MAAA,EAAA,CAAA,glBAAA,CAAA,EAAA;8BAItC,OAAO,EAAA,CAAA;sBAAf;gBACQ,OAAO,EAAA,CAAA;sBAAf;;;MEFU,oBAAoB,CAAA;IACtB,OAAO,GAAG,EAAE;IACZ,OAAO,GAAG,EAAE;IACZ,OAAO,GAA4D,MAAM;wGAHvE,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;4FAApB,oBAAoB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECVjC,woBAgBM,EAAA,MAAA,EAAA,CAAA,yvEAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EDXM,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAKX,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAPhC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,kBAAkB,WACnB,CAAC,YAAY,CAAC,EAGN,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,woBAAA,EAAA,MAAA,EAAA,CAAA,yvEAAA,CAAA,EAAA;8BAGtC,OAAO,EAAA,CAAA;sBAAf;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,OAAO,EAAA,CAAA;sBAAf;;;MEJU,qBAAqB,CAAA;wGAArB,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;4FAArB,qBAAqB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECTlC,gmBAiBM,EAAA,MAAA,EAAA,CAAA,kkBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EDbM,YAAY,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAKX,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAPjC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,mBAAmB,WACpB,CAAC,YAAY,CAAC,EAGN,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,gmBAAA,EAAA,MAAA,EAAA,CAAA,kkBAAA,CAAA,EAAA;;;MEapC,cAAc,CAAA;;IAEhB,IAAI,GAAG,KAAK;;IAGZ,gBAAgB,GAAgC,QAAQ;;IAGxD,QAAQ,GAA4F,QAAQ;;IAG5G,KAAK,GAAG,SAAS;IACjB,OAAO,GAAG,gCAAgC;IAC1C,SAAS,GAAG,IAAI;;IAGhB,cAAc,GAAG,IAAI;IACrB,cAAc,GAAG,cAAc;IAC/B,SAAS,GAAG,OAAO;IACnB,QAAQ,GAAG,EAAE;;IAGb,SAAS,GAAG,IAAI;IAChB,aAAa,GAAG,GAAG;IACnB,cAAc,GAAG,SAAS;IAC1B,aAAa,GAAG,EAAE;;IAGlB,iBAAiB,GAAG,KAAK;IACzB,cAAc,GAAG,aAAa;IAC9B,cAAc,GAAG,EAAE;IACnB,eAAe,GAAG,OAAO;IACzB,aAAa,GAAG,EAAE;;IAGnB,WAAW,GAAW,OAAO;IAErC,IACI,UAAU,CAAC,KAAsB,EAAA;AACnC,QAAA,IAAI,CAAC,WAAW,GAAG,OAAO,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,CAAA,EAAA,CAAI,GAAG,KAAK;;AAErE,IAAA,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW;;;IAIhB,WAAW,GAAG,KAAK;IACnB,UAAU,GAAG,KAAK;;IAGlB,iBAAiB,GAAG,QAAQ;IAC5B,gBAAgB,GAAiC,OAAO;IACxD,mBAAmB,GAA4B,WAAW;IAC1D,sBAAsB,GAAG,EAAE;;IAG3B,kBAAkB,GAAG,SAAS;IAC9B,iBAAiB,GAAiC,OAAO;IACzD,oBAAoB,GAA4B,SAAS;IACzD,uBAAuB,GAAG,EAAE;;AAG3B,IAAA,OAAO,GAAG,IAAI,YAAY,EAAQ,CAAC;AACnC,IAAA,MAAM,GAAG,IAAI,YAAY,EAAQ,CAAC;AAClC,IAAA,MAAM,GAAG,IAAI,YAAY,EAAQ,CAAC;;IAG5C,SAAS,GAAA;AACP,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;QACnB,IAAI,CAAC,UAAU,EAAE;;;IAInB,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;QAClB,IAAI,CAAC,UAAU,EAAE;;;IAInB,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;;AAGpB;;;AAGG;IACH,eAAe,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;;wGAxFxD,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;4FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,KAAA,EAAA,OAAA,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,aAAA,EAAA,eAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,aAAA,EAAA,UAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,sBAAA,EAAA,wBAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,uBAAA,EAAA,yBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECpB3B,ikFAuDM,EDxCM,MAAA,EAAA,CAAA,wkFAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,kbAAE,WAAW,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,aAAa,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,QAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,eAAe,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,OAAA,EAAA,QAAA,EAAA,MAAA,EAAA,UAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,UAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,cAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAKxD,cAAc,EAAA,UAAA,EAAA,CAAA;kBAR1B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAW,EACT,UAAA,EAAA,IAAI,EACP,OAAA,EAAA,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,EAAE,eAAe,CAAC,EAGnD,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,ikFAAA,EAAA,MAAA,EAAA,CAAA,wkFAAA,CAAA,EAAA;8BAItC,IAAI,EAAA,CAAA;sBAAZ;gBAGQ,gBAAgB,EAAA,CAAA;sBAAxB;gBAGQ,QAAQ,EAAA,CAAA;sBAAhB;gBAGQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBAGQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBAGQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBAGQ,iBAAiB,EAAA,CAAA;sBAAzB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBAMG,UAAU,EAAA,CAAA;sBADb;gBASQ,WAAW,EAAA,CAAA;sBAAnB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBAGQ,iBAAiB,EAAA,CAAA;sBAAzB;gBACQ,gBAAgB,EAAA,CAAA;sBAAxB;gBACQ,mBAAmB,EAAA,CAAA;sBAA3B;gBACQ,sBAAsB,EAAA,CAAA;sBAA9B;gBAGQ,kBAAkB,EAAA,CAAA;sBAA1B;gBACQ,iBAAiB,EAAA,CAAA;sBAAzB;gBACQ,oBAAoB,EAAA,CAAA;sBAA5B;gBACQ,uBAAuB,EAAA,CAAA;sBAA/B;gBAGS,OAAO,EAAA,CAAA;sBAAhB;gBACS,MAAM,EAAA,CAAA;sBAAf;gBACS,MAAM,EAAA,CAAA;sBAAf;;;MExEU,aAAa,CAAA;IACf,KAAK,GAAG,aAAa;IACrB,KAAK,GAA+E,SAAS;IAC7F,IAAI,GAAiC,QAAQ;IAC7C,SAAS,GAAY,KAAK;AAEnC,IAAA,UAAU,CAAC,KAAa,EAAA;AACtB,QAAA,OAAO,wBAAwB,CAAC,IAAI,CAAC,KAAK,CAAC;;wGAPlC,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;4FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA,MAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECZ1B,6LAQA,EAAA,MAAA,EAAA,CAAA,61BAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EDFY,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;4FAMX,aAAa,EAAA,UAAA,EAAA,CAAA;kBATzB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,UAAU,EACR,UAAA,EAAA,IAAI,EACP,OAAA,EAAA,CAAC,YAAY,CAAC,EAGN,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAAA,6LAAA,EAAA,MAAA,EAAA,CAAA,61BAAA,CAAA,EAAA;8BAG5B,KAAK,EAAA,CAAA;sBAAb;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,SAAS,EAAA,CAAA;sBAAjB;;;AEZH;;;AAGG;MAQU,eAAe,CAAA;;AAEjB,IAAA,KAAK;;IAEL,KAAK,GAAgF,SAAS;;IAE9F,OAAO,GAA0B,QAAQ;;IAEzC,IAAI,GAAuB,IAAI;;IAE/B,IAAI,GAAG,KAAK;;IAEZ,SAAS,GAAG,KAAK;;IAEjB,QAAQ,GAAG,KAAK;;AAEhB,IAAA,IAAI;;IAEJ,YAAY,GAAoB,OAAO;;AAEvC,IAAA,MAAM;;AAEN,IAAA,WAAW;;AAEX,IAAA,WAAW;;AAEX,IAAA,SAAS;;AAGR,IAAA,OAAO,GAAG,IAAI,YAAY,EAAQ;;AAElC,IAAA,OAAO,GAAG,IAAI,YAAY,EAAQ;;AAG5C,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ;;;AAI5D,IAAA,QAAQ,CAAC,KAAY,EAAA;QACnB,KAAK,CAAC,eAAe,EAAE;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;;;;IAKvB,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;;;wGAjDZ,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,eAAe,ECf5B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,OAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,MAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,IAAA,EAAA,MAAA,EAAA,YAAA,EAAA,cAAA,EAAA,MAAA,EAAA,QAAA,EAAA,WAAA,EAAA,aAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,4/DA+DA,EDlDY,MAAA,EAAA,CAAA,40JAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,sTAAE,aAAa,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,QAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,CAAA;;4FAE1B,eAAe,EAAA,UAAA,EAAA,CAAA;kBAP3B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,SAAS,cAGP,IAAI,EAAA,OAAA,EACP,CAAC,YAAY,EAAE,aAAa,CAAC,EAAA,QAAA,EAAA,4/DAAA,EAAA,MAAA,EAAA,CAAA,40JAAA,CAAA,EAAA;8BAI7B,KAAK,EAAA,CAAA;sBAAb;gBAEQ,KAAK,EAAA,CAAA;sBAAb;gBAEQ,OAAO,EAAA,CAAA;sBAAf;gBAEQ,IAAI,EAAA,CAAA;sBAAZ;gBAEQ,IAAI,EAAA,CAAA;sBAAZ;gBAEQ,SAAS,EAAA,CAAA;sBAAjB;gBAEQ,QAAQ,EAAA,CAAA;sBAAhB;gBAEQ,IAAI,EAAA,CAAA;sBAAZ;gBAEQ,YAAY,EAAA,CAAA;sBAApB;gBAEQ,MAAM,EAAA,CAAA;sBAAd;gBAEQ,WAAW,EAAA,CAAA;sBAAnB;gBAEQ,WAAW,EAAA,CAAA;sBAAnB;gBAEQ,SAAS,EAAA,CAAA;sBAAjB;gBAGS,OAAO,EAAA,CAAA;sBAAhB;gBAES,OAAO,EAAA,CAAA;sBAAhB;;;MEpBU,qBAAqB,CAAA;AACvB,IAAA,QAAQ;IACR,MAAM,GAAW,OAAO;AACxB,IAAA,eAAe;IACf,kBAAkB,GAAW,EAAE;IAC/B,uBAAuB,GAAW,EAAE;wGALlC,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;4FAArB,qBAAqB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,QAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,uBAAA,EAAA,yBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC1BlC,izEA0CM,EDrBM,MAAA,EAAA,CAAA,ysFAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,qgBAAe,aAAa,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,eAAe,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,MAAA,EAAA,WAAA,EAAA,UAAA,EAAA,MAAA,EAAA,cAAA,EAAA,QAAA,EAAA,aAAA,EAAA,aAAA,EAAA,WAAA,CAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,aAAa,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,QAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAKvE,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAPjC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,mBAAmB,WACpB,CAAC,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,aAAa,EAAE,eAAe,EAAE,aAAa,CAAC,EAGlE,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,izEAAA,EAAA,MAAA,EAAA,CAAA,ysFAAA,CAAA,EAAA;8BAGtC,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACQ,kBAAkB,EAAA,CAAA;sBAA1B;gBACQ,uBAAuB,EAAA,CAAA;sBAA/B;;;MEnBU,kBAAkB,CAAA;IACpB,QAAQ,GAAW,EAAE;IACrB,IAAI,GAAW,EAAE;IACjB,KAAK,GAAW,uBAAuB;AACvC,IAAA,aAAa,GAAwC,MAAM,CAAA;IACpE,eAAe,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,KAAK,IAAI,IAAI,CAAC,aAAa,KAAK,QAAQ,EAAE;AACnE,YAAA,OAAO,2BAA2B;;AAEpC,QAAA,OAAO,6BAA6B;;wGAT3B,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,kBAAkB,ECZ/B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,m5DA+CW,EDxCC,MAAA,EAAA,CAAA,qkCAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,aAAa,oFAAE,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,YAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,cAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAK1B,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAR9B,SAAS;+BACE,gBAAgB,EAAA,UAAA,EACd,IAAI,EAAA,OAAA,EACP,CAAC,aAAa,EAAE,YAAY,CAAC,EAAA,eAAA,EAGrB,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,m5DAAA,EAAA,MAAA,EAAA,CAAA,qkCAAA,CAAA,EAAA;8BAGtC,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,aAAa,EAAA,CAAA;sBAArB;;;MEDU,iBAAiB,CAAA;IACnB,QAAQ,GAAG,aAAa;IACxB,KAAK,GAAW,EAAE;IAClB,KAAK,GAAoB,EAAE;IAC3B,WAAW,GAAW,EAAE;IACxB,KAAK,GAAG,CAAC;IACT,IAAI,GAAoC,SAAS;IACjD,SAAS,GAAW,EAAE;IACtB,SAAS,GAAW,CAAC;IACrB,UAAU,GAAW,EAAE;IACvB,IAAI,GAAW,EAAE;IACjB,IAAI,GAAW,EAAE;IACjB,QAAQ,GAAQ,EAAE;AACjB,IAAA,SAAS,GAAG,IAAI,YAAY,EAAQ;AACpC,IAAA,SAAS,GAAG,IAAI,YAAY,EAAQ;IAE9C,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;;AAEvB,IAAA,WAAW,CAAC,IAAS,EAAA;AACnB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;;wGApBhB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,iBAAiB,8XCf9B,y3HA8FM,EAAA,MAAA,EAAA,CAAA,0/JAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EDrFM,YAAY,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,aAAa,2JAAE,aAAa,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;4FAMzC,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAT7B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,eAAe,cACb,IAAI,EAAA,OAAA,EACP,CAAC,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,mBAGnD,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAAA,y3HAAA,EAAA,MAAA,EAAA,CAAA,0/JAAA,CAAA,EAAA;8BAG5B,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACS,SAAS,EAAA,CAAA;sBAAlB;gBACS,SAAS,EAAA,CAAA;sBAAlB;;;MEFU,iBAAiB,CAAA;AAgClB,IAAA,UAAA;AACA,IAAA,GAAA;IAhCD,aAAa,GAAW,mBAAmB;IAC3C,OAAO,GAAqB,EAAE;IAC9B,UAAU,GAAwC,EAAE;IACpD,eAAe,GAAa,EAAE;IAC9B,WAAW,GAAa,EAAE;IAC1B,MAAM,GAAY,KAAK;IACvB,YAAY,GAAY,KAAK;IAC7B,aAAa,GAAW,EAAE;IAC1B,YAAY,GAAY,KAAK;AAC7B,IAAA,YAAY,GAAW,cAAc,CAAC;AACtC,IAAA,QAAQ,GAAY,KAAK,CAAC;AAEzB,IAAA,eAAe,GAAG,IAAI,YAAY,EAAO;AACzC,IAAA,WAAW,GAAG,IAAI,YAAY,EAAO;IAE/C,MAAM,GAAG,KAAK;IACd,UAAU,GAAG,EAAE;IACf,eAAe,GAAqB,EAAE;IACtC,eAAe,GAAqB,EAAE;IACtC,cAAc,GAAkB,IAAI;;IAGpC,kBAAkB,GAAG,CAAC,CAAC;IACvB,qBAAqB,GAAG,CAAC,CAAC;IAC1B,sBAAsB,GAAG,KAAK;;AAGtB,IAAA,OAAO,YAAY,GAAwB,EAAE;IAErD,KAAK,GAAG,EAAE;IACV,WACU,CAAA,UAAsB,EACtB,GAAsB,EAAA;QADtB,IAAU,CAAA,UAAA,GAAV,UAAU;QACV,IAAG,CAAA,GAAA,GAAH,GAAG;;AAGX,QAAA,iBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;;;AAGnC,IAAA,QAAQ,GAAG,CAAC,KAAU,KAAI,GAAI;AAC9B,IAAA,SAAS,GAAG,MAAK,GAAI;;AAG7B,IAAA,UAAU,CAAC,GAAQ,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,GAAG;;;AAIlB,IAAA,gBAAgB,CAAC,EAAO,EAAA;AACtB,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;AAGpB,IAAA,iBAAiB,CAAC,EAAO,EAAA;AACvB,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;;IAIrB,QAAQ,GAAA;QACN,IAAI,CAAC,eAAe,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;;IAG1C,WAAW,GAAA;;QAET,MAAM,KAAK,GAAG,iBAAiB,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC;AAC1D,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACd,iBAAiB,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;;;;IAK3C,OAAO,uBAAuB,CAAC,eAAkC,EAAA;QACvE,OAAO,CAAC,GAAG,CAAC,2CAA2C,EAAE,iBAAiB,CAAC,YAAY,CAAC,MAAM,CAAC;AAC/F,QAAA,iBAAiB,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,IAAG;YAChD,IAAI,QAAQ,KAAK,eAAe,IAAI,QAAQ,CAAC,MAAM,EAAE;AACnD,gBAAA,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;AAC/B,gBAAA,QAAQ,CAAC,MAAM,GAAG,KAAK;AACvB,gBAAA,QAAQ,CAAC,cAAc,GAAG,IAAI;gBAC9B,QAAQ,CAAC,gBAAgB,EAAE;;AAE3B,gBAAA,QAAQ,CAAC,GAAG,CAAC,aAAa,EAAE;;AAEhC,SAAC,CAAC;;IAGJ,cAAc,GAAA;;AAEZ,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB;;QAGF,OAAO,CAAC,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAAC,MAAM,CAAC;AAClE,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;;AAEf,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;AACnB,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI;YAC1B,IAAI,CAAC,gBAAgB,EAAE;;aAClB;;AAEL,YAAA,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC;AACxD,YAAA,iBAAiB,CAAC,uBAAuB,CAAC,IAAI,CAAC;AAC/C,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;;IAItB,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;AACnB,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB,IAAI,CAAC,eAAe,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACxC,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;QAC1B,IAAI,CAAC,gBAAgB,EAAE;;IAGjB,gBAAgB,GAAA;AACtB,QAAA,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;;IAGrC,QAAQ,GAAA;QACN,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE;YAC3B,IAAI,CAAC,eAAe,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;YACxC;;QAGF,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;QACjD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,IAAG;;AAElD,YAAA,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC;;AAGzE,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,SAAS,IAClE,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CACnD,IAAI,KAAK;YAEV,OAAO,iBAAiB,IAAI,eAAe;AAC7C,SAAC,CAAC;;AAGJ,IAAA,YAAY,CAAC,MAAsB,EAAA;;QAEjC,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;YACpC;;QAGF,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;AAC9C,YAAA,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;;aAC/B;AACL,YAAA,IAAI,CAAC,eAAe,GAAG,CAAC,MAAM,CAAC;AAC/B,YAAA,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,IAAI;;AAEhC,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;AACnB,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI;YAC1B,IAAI,CAAC,WAAW,EAAE;;AAEpB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa;AAC/B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC;QACjC,IAAI,CAAC,SAAS,EAAE;;AAGlB,IAAA,uBAAuB,CAAC,MAAsB,EAAA;;AAE5C,QAAA,IAAI,MAAM,CAAC,QAAQ,EAAE;YACnB;;QAGF,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC;AAC/E,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;;aAChC;AACL,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,gBAAA,IAAI,CAAC,eAAe,GAAG,CAAC,MAAM,CAAC;;iBAC1B;AACL,gBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC;;;QAGrC,IAAI,CAAC,WAAW,EAAE;;AAGpB,IAAA,eAAe,CAAC,SAAyB,EAAA;;QAEvC,IAAI,SAAS,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;YACvC;;;AAIF,QAAA,IAAI,CAAC,eAAe,GAAG,CAAC,SAAS,CAAC;AAClC,QAAA,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,IAAI;;AAGnC,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;AACnB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;QAC1B,IAAI,CAAC,WAAW,EAAE;;AAGpB,IAAA,gBAAgB,CAAC,UAAkB,EAAA;;AAEjC,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB;;;AAIF,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,KAAK,UAAU,GAAG,IAAI,GAAG,UAAU;;AAG9E,IAAA,gBAAgB,CAAC,MAAsB,EAAA;AACrC,QAAA,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC;;AAGrE,IAAA,mBAAmB,CAAC,SAAyB,EAAA;AAC3C,QAAA,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,CAAC;;AAGxE,IAAA,aAAa,CAAC,UAAkB,EAAA;QAC9B,OAAO,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;;AAGlF,IAAA,aAAa,CAAC,MAAsB,EAAA;;AAElC,QAAA,IAAI,MAAM,CAAC,IAAI,EAAE;YACf,OAAO,MAAM,CAAC,IAAI;;;AAGpB,QAAA,OAAO,cAAc;;AAGvB,IAAA,cAAc,CAAC,MAAsB,EAAA;;AAEnC,QAAA,OAAO,CAAC,EAAE,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;;AAGlE,IAAA,gBAAgB,CAAC,MAAsB,EAAA;QACrC,OAAO,CAAC,EAAE,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC;;AAG7C,IAAA,mBAAmB,CAAC,SAAyB,EAAA;QAC3C,OAAO,CAAC,EAAE,SAAS,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC;;IAGhD,cAAc,GAAA;QACZ,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;YACrC,OAAO,IAAI,CAAC,aAAa;;QAE3B,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;YACrC,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI;;AAErC,QAAA,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,iBAAiB;;IAGxD,WAAW,GAAA;AACT,QAAA,MAAM,IAAI,GAAG;YACX,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,aAAa,EAAE,IAAI,CAAC;SACrB;AACD,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;AAC/B,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;;AAI7B,IAAA,eAAe,CAAC,KAAY,EAAA;AAC1B,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAqB;QAC1C,MAAM,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC;;AAGtD,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACnD,IAAI,CAAC,aAAa,EAAE;;;QAItB,IAAI,eAAe,IAAI,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;AACzD,YAAA,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC;AACtD,YAAA,iBAAiB,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,IAAG;AAChD,gBAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,aAAa,KAAK,eAAe,IAAI,QAAQ,CAAC,MAAM,EAAE;AAC5E,oBAAA,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC;AACxD,oBAAA,QAAQ,CAAC,MAAM,GAAG,KAAK;AACvB,oBAAA,QAAQ,CAAC,cAAc,GAAG,IAAI;oBAC9B,QAAQ,CAAC,gBAAgB,EAAE;AAC3B,oBAAA,QAAQ,CAAC,GAAG,CAAC,aAAa,EAAE;;AAEhC,aAAC,CAAC;;;;AAKN,IAAA,aAAa,CAAC,KAAY,EAAA;QACxB,KAAK,CAAC,eAAe,EAAE;QACvB,IAAI,CAAC,cAAc,EAAE;;;AAIvB,IAAA,eAAe,CAAC,KAAoB,EAAA;AAClC,QAAA,MAAM,OAAO,GAA+B;AAC1C,YAAA,OAAO,EAAE,MAAM,IAAI,CAAC,sBAAsB,EAAE;AAC5C,YAAA,GAAG,EAAE,MAAM,IAAI,CAAC,sBAAsB,EAAE;YACxC,WAAW,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;YAC7C,SAAS,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YAC1C,QAAQ,EAAE,MAAM,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa;SAClD;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;QACjC,IAAI,MAAM,EAAE;YACV,KAAK,CAAC,cAAc,EAAE;AACtB,YAAA,MAAM,EAAE;;;AAIZ,IAAA,iBAAiB,CAAC,KAAoB,EAAA;QACpC,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE;AAElB,QAAA,MAAM,OAAO,GAA+B;AAC1C,YAAA,QAAQ,EAAE,MAAQ,EAAA,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;AAC7D,YAAA,KAAK,EAAE,MAAM,IAAI,CAAC,aAAa;SAChC;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;QACjC,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,KAAK,CAAC,GAAG,KAAK,KAAK;gBAAE,KAAK,CAAC,cAAc,EAAE;AAC/C,YAAA,MAAM,EAAE;;;AAIZ,IAAA,eAAe,CAAC,KAAoB,EAAA;AAClC,QAAA,MAAM,OAAO,GAA+B;YAC1C,WAAW,EAAE,MAAK;gBAChB,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;AACnC,oBAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;;aAEtB;YACD,SAAS,EAAE,MAAK;gBACd,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;oBACnC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;;aAEpD;YACD,QAAQ,EAAE,MAAK;gBACb,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,WAAW,EAAE;aACnB;YACD,OAAO,EAAE,MAAK;gBACZ,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;oBACnC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;;SAG/C;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;QACjC,IAAI,MAAM,EAAE;YACV,KAAK,CAAC,cAAc,EAAE;AACtB,YAAA,MAAM,EAAE;;;IAIZ,eAAe,CAAC,KAAoB,EAAE,MAAsB,EAAA;AAC1D,QAAA,MAAM,OAAO,GAA+B;YAC1C,OAAO,EAAE,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC;YAClD,GAAG,EAAE,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC;YAC9C,WAAW,EAAE,MAAM,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YAC1C,SAAS,EAAE,MAAM,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACzC,YAAY,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;AACjD,YAAA,QAAQ,EAAE,MAAQ,EAAA,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;YAC7D,MAAM,EAAE,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;AACjC,YAAA,KAAK,EAAE,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC;SAC9D;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;QACjC,IAAI,MAAM,EAAE;YACV,KAAK,CAAC,cAAc,EAAE;AACtB,YAAA,MAAM,EAAE;;;IAIZ,kBAAkB,CAAC,KAAoB,EAAE,SAAyB,EAAA;AAChE,QAAA,MAAM,OAAO,GAA+B;YAC1C,OAAO,EAAE,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;YAC9C,GAAG,EAAE,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;YAC1C,WAAW,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAC7C,SAAS,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YAC5C,WAAW,EAAE,MAAK,EAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE;AAC7F,YAAA,QAAQ,EAAE,MAAQ,EAAA,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;SAC5D;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;QACjC,IAAI,MAAM,EAAE;YACV,KAAK,CAAC,cAAc,EAAE;AACtB,YAAA,MAAM,EAAE;;;;IAKJ,sBAAsB,GAAA;QAC5B,IAAI,CAAC,cAAc,EAAE;AACrB,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;gBACpC,IAAI,CAAC,gBAAgB,EAAE;;iBAClB;AACL,gBAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;;;;AAKjB,IAAA,YAAY,CAAC,QAA0B,EAAA;QAC7C,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,cAAc,EAAE;QAEvC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;YACpC,IAAI,CAAC,gBAAgB,EAAE;;aAClB;YACL,IAAI,CAAC,WAAW,CAAC,QAAQ,KAAK,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;;;AAIxE,IAAA,sBAAsB,CAAC,MAAsB,EAAA;QACnD,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;AACnC,YAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC;AAClC,YAAA,IAAI,IAAI,CAAC,cAAc,KAAK,MAAM,CAAC,IAAI;AAAE,gBAAA,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;;aAC1D;AACL,YAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;;;AAIrB,IAAA,gBAAgB,CAAC,MAAsB,EAAA;QAC7C,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;AACnC,YAAA,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,IAAI;AACjC,YAAA,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;;;AAIlB,IAAA,eAAe,CAAC,SAAiB,EAAA;AACvC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,GAAG,SAAS;;QAGpD,IAAI,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,kBAAkB,KAAK,CAAC,KAAK,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE;YAC3F,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,EAAE;YACvB;;AAGF,QAAA,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;AAC3D,YAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;;;AAItB,IAAA,kBAAkB,CAAC,SAAiB,EAAA;QAC1C,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,kBAAkB,CAAC;QACnE,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC;AACtD,QAAA,IAAI,CAAC,UAAU;YAAE;AAEjB,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,qBAAqB,GAAG,SAAS;QACvD,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE;AACjD,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;;;AAIzB,IAAA,WAAW,CAAC,KAAa,EAAA;AAC/B,QAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK;AAC/B,QAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;AACnC,QAAA,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC;AAC/B,QAAA,UAAU,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;;AAGlD,IAAA,cAAc,CAAC,KAAa,EAAA;AAClC,QAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK;AAClC,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI;AAClC,QAAA,UAAU,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;;IAGrD,YAAY,CAAC,QAAgB,EAAE,KAAa,EAAA;AAClD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AACzE,QAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AAClB,YAAA,QAAQ,CAAC,KAAK,CAAiB,CAAC,KAAK,EAAE;;;IAIpC,gBAAgB,GAAA;QACtB,UAAU,CAAC,MAAK;AACd,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,mBAAmB,CAAC;AACpF,YAAA,IAAI,WAAW;gBAAG,WAA2B,CAAC,KAAK,EAAE;SACtD,EAAE,CAAC,CAAC;;IAGC,WAAW,GAAA;AACjB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,kBAAkB,CAAC;AAC9E,QAAA,IAAI,MAAM;YAAG,MAAsB,CAAC,KAAK,EAAE;;;AAI7C,IAAA,oBAAoB,CAAC,WAAmB,EAAA;;QAEtC,MAAM,YAAY,GAAG,EAAE;QACvB,MAAM,eAAe,GAAG,CAAC;AACzB,QAAA,MAAM,eAAe,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,IAAI,EAAE,GAAG,CAAC;AACnE,QAAA,MAAM,YAAY,GAAG,EAAE,CAAC;AACxB,QAAA,MAAM,uBAAuB,GAAG,CAAC,CAAC;;AAGlC,QAAA,OAAO,YAAY,GAAG,eAAe,GAAG,eAAe,GAAG,uBAAuB,IAAI,WAAW,GAAG,YAAY,CAAC;;wGA5evG,iBAAiB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,iBAAiB,EARjB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,MAAA,EAAA,QAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,eAAA,EAAA,YAAA,EAAA,cAAA,EAAA,YAAA,EAAA,cAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,gBAAA,EAAA,yBAAA,EAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,iBAAiB;AAC1B,gBAAA,WAAW,EAAE,UAAU,CAAC,MAAM,iBAAiB,CAAC;AAChD,gBAAA,KAAK,EAAE;AACR;SACF,ECzBH,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,gtKAqHA,+sTDtGY,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAE,WAAW,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAA,IAAA,CAAA,oBAAA,EAAA,QAAA,EAAA,8MAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAA,IAAA,CAAA,eAAA,EAAA,QAAA,EAAA,2CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAA,IAAA,CAAA,OAAA,EAAA,QAAA,EAAA,qDAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,SAAA,EAAA,gBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,iBAAiB,sKAAE,aAAa,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,QAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,CAAA;;4FAY1D,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAd7B,SAAS;+BACE,cAAc,EAAA,OAAA,EACf,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,aAAa,CAAC,EAI3D,SAAA,EAAA;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAE,UAAU,CAAC,uBAAuB,CAAC;AAChD,4BAAA,KAAK,EAAE;AACR;AACF,qBAAA,EAAA,QAAA,EAAA,gtKAAA,EAAA,MAAA,EAAA,CAAA,wpTAAA,CAAA,EAAA;+GAGQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBAES,eAAe,EAAA,CAAA;sBAAxB;gBACS,WAAW,EAAA,CAAA;sBAApB;gBAoPD,eAAe,EAAA,CAAA;sBADd,YAAY;uBAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC;;;MEhR/B,gBAAgB,CAAA;AAeP,IAAA,GAAA;IAdX,KAAK,GAAW,OAAO;IACvB,cAAc,GAAW,MAAM;IAC/B,MAAM,GAAW,OAAO;IACxB,cAAc,GAAW,MAAM;IAC/B,kBAAkB,GAAY,KAAK;IACnC,aAAa,GAAyB,QAAQ;IAC9C,UAAU,GAAY,IAAI;IAC1B,UAAU,GAAY,IAAI;IAC1B,WAAW,GAAY,KAAK;AAE3B,IAAA,cAAc,GAAG,IAAI,YAAY,EAAW;IAE9C,YAAY,GAAG,KAAK;AAE5B,IAAA,WAAA,CAAoB,GAAsB,EAAA;QAAtB,IAAG,CAAA,GAAA,GAAH,GAAG;;IAEvB,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW;;AAGtC,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE;AACjE,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW;AACpC,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;;IAI3B,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,YAAY;QACtC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;AAC3C,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGzB,IAAA,IAAI,YAAY,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK;;AAG7D,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,YAAY;;wGAvCf,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,gBAAgB,ECZ7B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,QAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,6wDAgEA,ED1DY,MAAA,EAAA,CAAA,41EAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,mIAAE,eAAe,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,OAAA,EAAA,QAAA,EAAA,MAAA,EAAA,UAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,UAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,cAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;4FAM5B,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAR5B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,aAAa,EACd,OAAA,EAAA,CAAC,YAAY,EAAE,eAAe,CAAC,EAGvB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAAA,6wDAAA,EAAA,MAAA,EAAA,CAAA,41EAAA,CAAA,EAAA;sFAG5B,KAAK,EAAA,CAAA;sBAAb;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,kBAAkB,EAAA,CAAA;sBAA1B;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBAES,cAAc,EAAA,CAAA;sBAAvB;;;MELU,eAAe,CAAA;AAkBN,IAAA,UAAA;IAjBX,GAAG,GAAG,CAAC;IACP,GAAG,GAAG,GAAG;IACT,KAAK,GAAG,CAAC;IACT,IAAI,GAAG,CAAC;IACR,WAAW,GAAG,IAAI;AAEjB,IAAA,WAAW,GAAG,IAAI,YAAY,EAAU;AACxB,IAAA,WAAW;IAErC,SAAS,GAAG,KAAK;IACjB,UAAU,GAAG,KAAK;AAEV,IAAA,QAAQ,GAA4B,MAAK,GAAG;AAC5C,IAAA,SAAS,GAAe,MAAK,GAAG;AAExC,IAAA,gBAAgB,GAA2B,MAAM,CAAC,CAAC,CAAC;AAEpD,IAAA,WAAA,CAAoB,UAAsB,EAAA;QAAtB,IAAU,CAAA,UAAA,GAAV,UAAU;;AAE9B,IAAA,IAAI,UAAU,GAAA;QACZ,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG;;;AAIhE,IAAA,UAAU,CAAC,KAAa,EAAA;AACtB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC;;;AAIzB,IAAA,gBAAgB,CAAC,EAAO,EAAA;AACtB,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;AAGpB,IAAA,iBAAiB,CAAC,EAAO,EAAA;AACvB,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;AAGrB,IAAA,YAAY,CAAC,KAAiB,EAAA;AAC5B,QAAA,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;;AAGlC,IAAA,SAAS,CAAC,KAAiB,EAAA;QACzB,KAAK,CAAC,cAAc,EAAE;AACtB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI;AACtB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;AAGvB,IAAA,SAAS,CAAC,KAAoB,EAAA;AAC5B,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK;AACzB,QAAA,QAAQ,KAAK,CAAC,GAAG;AACf,YAAA,KAAK,YAAY;AACjB,YAAA,KAAK,SAAS;gBACZ,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI;gBACjC;AACF,YAAA,KAAK,WAAW;AAChB,YAAA,KAAK,WAAW;gBACd,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI;gBACjC;AACF,YAAA,KAAK,MAAM;AACT,gBAAA,QAAQ,GAAG,IAAI,CAAC,GAAG;gBACnB;AACF,YAAA,KAAK,KAAK;AACR,gBAAA,QAAQ,GAAG,IAAI,CAAC,GAAG;gBACnB;AACF,YAAA;gBACE;;QAEJ,KAAK,CAAC,cAAc,EAAE;AACtB,QAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;;AAI5B,IAAA,WAAW,CAAC,KAAiB,EAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;;;IAKpC,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK;AACvB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;YACtB,IAAI,CAAC,SAAS,EAAE;;;AAIpB,IAAA,mBAAmB,CAAC,KAAa,EAAA;AACjC,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;;AAG/C,IAAA,oBAAoB,CAAC,KAAiB,EAAA;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,qBAAqB,EAAE;AACnE,QAAA,MAAM,UAAU,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK;AAC3D,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,GAAG,UAAU,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AAC9D,QAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;;AAGpB,IAAA,WAAW,CAAC,KAAa,EAAA;AAC/B,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI;QAC9D,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;AACzE,QAAA,IAAI,YAAY,KAAK,IAAI,CAAC,KAAK,EAAE;AAC/B,YAAA,IAAI,CAAC,KAAK,GAAG,YAAY;AACzB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;YACzB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;;;wGAzG1B,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,eAAe,EARf,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,KAAA,EAAA,GAAA,EAAA,KAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA,MAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,kBAAA,EAAA,aAAA,EAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,iBAAiB;AAC1B,gBAAA,WAAW,EAAE,UAAU,CAAC,MAAM,eAAe,CAAC;AAC9C,gBAAA,KAAK,EAAE;AACR;SACF,EChBH,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,aAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,i1BAeM,8uFDTM,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAYX,eAAe,EAAA,UAAA,EAAA,CAAA;kBAd3B,SAAS;+BACE,YAAY,EAAA,OAAA,EACb,CAAC,YAAY,CAAC,mBAGN,uBAAuB,CAAC,MAAM,EACpC,SAAA,EAAA;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAE,UAAU,CAAC,qBAAqB,CAAC;AAC9C,4BAAA,KAAK,EAAE;AACR;AACF,qBAAA,EAAA,QAAA,EAAA,i1BAAA,EAAA,MAAA,EAAA,CAAA,urFAAA,CAAA,EAAA;+EAGQ,GAAG,EAAA,CAAA;sBAAX;gBACQ,GAAG,EAAA,CAAA;sBAAX;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBAES,WAAW,EAAA,CAAA;sBAApB;gBACyB,WAAW,EAAA,CAAA;sBAApC,SAAS;uBAAC,aAAa;gBAiExB,WAAW,EAAA,CAAA;sBADV,YAAY;uBAAC,oBAAoB,EAAE,CAAC,QAAQ,CAAC;gBAQ9C,SAAS,EAAA,CAAA;sBADR,YAAY;uBAAC,kBAAkB;;;MEpFrB,0BAA0B,CAAA;;IAG5B,iBAAiB,GAAG,KAAK;;IAGzB,KAAK,GAAG,OAAO;;IAGf,OAAO,GAAG,SAAS;;IAGnB,IAAI,GAAG,KAAK;;AAGX,IAAA,MAAM,GAAG,IAAI,YAAY,EAAQ;;AAK3C,IAAA,aAAa,CAAC,IAAY,EAAA;AACxB,QAAA,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;;;IAIrB,YAAY,GAAA;AACV,QAAA,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC;AAC7B,QAAA,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;;;IAIpB,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;;wGAjCT,0BAA0B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAA1B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,0BAA0B,uNCbvC,+vBAYM,EAAA,MAAA,EAAA,CAAA,ylBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EDJM,YAAY,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,cAAc,inBAAE,oBAAoB,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,aAAA,EAAA,SAAA,EAAA,MAAA,EAAA,UAAA,EAAA,UAAA,EAAA,OAAA,EAAA,QAAA,EAAA,MAAA,EAAA,IAAA,EAAA,MAAA,EAAA,WAAA,EAAA,WAAA,EAAA,UAAA,EAAA,WAAA,EAAA,OAAA,EAAA,WAAA,CAAA,EAAA,OAAA,EAAA,CAAA,cAAA,EAAA,eAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,cAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAKjD,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBARtC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,wBAAwB,EACtB,UAAA,EAAA,IAAI,EACP,OAAA,EAAA,CAAC,YAAY,EAAE,cAAc,EAAE,oBAAoB,CAAC,EAG5C,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,+vBAAA,EAAA,MAAA,EAAA,CAAA,ylBAAA,CAAA,EAAA;8BAKtC,iBAAiB,EAAA,CAAA;sBAAzB;gBAGQ,KAAK,EAAA,CAAA;sBAAb;gBAGQ,OAAO,EAAA,CAAA;sBAAf;gBAGQ,IAAI,EAAA,CAAA;sBAAZ;gBAGS,MAAM,EAAA,CAAA;sBAAf;;;AE5BI,MAAM,aAAa,GAAG;AAC3B,IAAA,YAAY,EAAE,kBAAkB;AAChC,IAAA,mBAAmB,EAAE,qBAAqB;AAC1C,IAAA,uBAAuB,EAAE,wBAAwB;AACjD,IAAA,sBAAsB,EAAE,2CAA2C;AACnE,IAAA,eAAe,EAAE,wBAAwB;AACzC,IAAA,qBAAqB,EAAE,4BAA4B;AACnD,IAAA,iBAAiB,EAAE,yBAAyB;AAC5C,IAAA,iBAAiB,EAAE,gFAAgF;AACnG,IAAA,eAAe,EAAE,iDAAiD;AAClE,IAAA,aAAa,EAAE,QAAQ;AACvB,IAAA,kBAAkB,EAAE,6BAA6B;AACjD,IAAA,aAAa,EAAG,CAAC,GAAG,IAAI,GAAG,IAAI;AAC/B,IAAA,kBAAkB,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;AACtF,IAAA,mBAAmB,EAAE,iDAAiD;AACtE,IAAA,eAAe,EAAE,4BAA4B;;AAM7C,IAAA,oBAAoB,EAAE,2BAA2B;AACjD,IAAA,kBAAkB,EAAE,2DAA2D;AAC/E,IAAA,oBAAoB,EAAE,gBAAgB;AACtC,IAAA,UAAU,EAAE,OAAO;AACnB,IAAA,0BAA0B,EAAE,yBAAyB;AACrD,IAAA,cAAc,EAAE,qCAAqC;AACrD,IAAA,aAAa,EAAE,QAAQ;AACvB,IAAA,wBAAwB,EAAE,mBAAmB;AAC7C,IAAA,oBAAoB,EAAE,gBAAgB;AACtC,IAAA,uBAAuB,EAAE,yDAAyD;AAClF,IAAA,WAAW,EAAE,aAAa;AAC1B,IAAA,eAAe,EAAE,qBAAqB;AACtC,IAAA,oBAAoB,EAAE,qCAAqC;AAC3D,IAAA,WAAW,EAAE,oBAAoB;AACjC,IAAA,aAAa,EAAE,mBAAmB;AAClC,IAAA,uBAAuB,EAAE,sEAAsE;AAC/F,IAAA,qBAAqB,EAAE,6EAA6E;AACpG,IAAA,YAAY,EAAE,OAAO;AACrB,IAAA,6BAA6B,EAAE,yBAAyB;AACxD,IAAA,uBAAuB,EAAE,qCAAqC;AAC9D,IAAA,oBAAoB,EAAE,oBAAoB;AAC1C,IAAA,cAAc,EAAE,8CAA8C;AAC9D,IAAA,oBAAoB,EAAE,gCAAgC;CAIvD;;MC9BY,mBAAmB,CAAA;AACb,IAAA,YAAY,GAAG,IAAI,YAAY,EAAQ;AACvC,IAAA,gBAAgB,GAAG,IAAI,YAAY,EAAU;IACrD,KAAK,GAAqB,OAAO;IACjC,UAAU,GAAW,EAAE;IACvB,eAAe,GAAY,KAAK;IAChC,cAAc,GAAa,EAAE;IAC7B,cAAc,GAAY,KAAK;IAC/B,QAAQ,GAAkB,IAAI;IAC9B,cAAc,GAAU,kBAAkB;IAC1C,oBAAoB,GAAG,wBAAwB;IAC/C,WAAW,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;IAEvC,YAAY,GAAG,aAAa;IACrB,aAAa,GAAW,EAAE;IAC1B,mBAAmB,GAAG,KAAK;IAC3B,eAAe,GAAG,KAAK;IACvB,aAAa,GAAG,KAAK;IACrB,aAAa,GAAG,KAAK;IACrB,cAAc,GAAG,KAAK;IACtB,OAAO,GAAY,KAAK;;IAGxB,QAAQ,GAAW,EAAE;IAE5B,QAAQ,GAAA;;QAEN,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,IAAI,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;;AAK7F,IAAA,IAAI,kBAAkB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;;IAElI,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;;AAE7B,IAAA,UAAU,CAAC,KAAa,EAAE,QAAA,GAAmB,CAAC,EAAA;AAC7C,QAAA,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC;AAAE,YAAA,OAAO,SAAS;QAEjD,MAAM,CAAC,GAAG,IAAI;AACd,QAAA,MAAM,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QAC/C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACnD,QAAA,MAAM,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAEpC,QAAA,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;;AAEtD,IAAA,cAAc,CAAC,KAAY,EAAA;AAChC,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,MAA0B;AAC9C,QAAA,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACzC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;AAG/B,YAAA,KAAK,CAAC,KAAK,GAAG,EAAE;;;;AAMb,IAAA,UAAU,CAAC,KAAgB,EAAA;QAChC,KAAK,CAAC,cAAc,EAAE;QACtB,KAAK,CAAC,eAAe,EAAE;;;AAKlB,IAAA,MAAM,CAAC,KAAgB,EAAA;QAC5B,KAAK,CAAC,cAAc,EAAE;QACtB,KAAK,CAAC,eAAe,EAAE;AACvB,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,YAAY,EAAE,KAAK;QACvC,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;;;AAKrB,IAAA,UAAU,CAAC,IAAU,EAAA;AAC3B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE;;AAE/D,QAAA,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;AACtE,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI;AAC3B,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;AAC1B,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;YAC1B;;QAGF,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE;AAChC,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;AACzB,YAAA,IAAI,CAAC,eAAe,GAAG,KAAK;AAC5B,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;YAC1B;;;QAIF,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;AAChG,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;AACzB,YAAA,IAAI,CAAC,eAAe,GAAG,KAAK;AAC5B,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;YAC1B;;;AAIF,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,YAAA,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC;;aACtB;;AAEL,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,IACtD,YAAY,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,YAAY,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC;YAErE,IAAI,CAAC,WAAW,EAAE;AAChB,gBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;;;QAIjC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;AAC9C,QAAA,IAAI,CAAC,eAAe,GAAG,KAAK;AAC5B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK;AAC1B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;IAGrB,gBAAgB,GAAA;;QAErB,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;AAChG,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;YACzB;;AAGF,QAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAqB;QAC3F,IAAI,SAAS,EAAE;YACb,SAAS,CAAC,KAAK,EAAE;;;IAId,UAAU,GAAA;QACf,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;;AAEjC,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,IAAG;AAChC,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9B,aAAC,CAAC;AACF,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;;aAC1B;;YAEL,IAAI,CAAC,gBAAgB,EAAE;;;AAIpB,IAAA,UAAU,CAAC,KAAa,EAAA;AAC7B,QAAA,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YACnD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YACnC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;;YAG9C,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;AACnC,gBAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK;;;AAIlC,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;;;IAIvB,WAAW,GAAA;AAChB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE;AACvB,QAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK;AAChC,QAAA,IAAI,CAAC,eAAe,GAAG,KAAK;AAC5B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK;AAC1B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;;IAGzC,WAAW,GAAA;QAChB,IAAI,CAAC,WAAW,EAAE;;wGA7KT,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;4FAAnB,mBAAmB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,OAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,UAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECjBhC,4gKAiGM,EAAA,MAAA,EAAA,CAAA,k9FAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EDtFM,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,CAAA,aAAA,EAAA,IAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,CAAA,aAAA,EAAA,IAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,aAAa,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,QAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAE,mBAAmB,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,eAAe,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,OAAA,EAAA,QAAA,EAAA,MAAA,EAAA,UAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,UAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,cAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,eAAe,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,MAAA,EAAA,WAAA,EAAA,UAAA,EAAA,MAAA,EAAA,cAAA,EAAA,QAAA,EAAA,aAAA,EAAA,aAAA,EAAA,WAAA,CAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAMjF,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAT/B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,iBAAiB,cACf,IAAI,EAAA,OAAA,EACP,CAAC,YAAY,EAAE,aAAa,EAAE,mBAAmB,EAAE,eAAe,EAAE,eAAe,CAAC,EAG5E,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,4gKAAA,EAAA,MAAA,EAAA,CAAA,k9FAAA,CAAA,EAAA;8BAI9B,YAAY,EAAA,CAAA;sBAA5B;gBACgB,gBAAgB,EAAA,CAAA;sBAAhC;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,oBAAoB,EAAA,CAAA;sBAA5B;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBAiDM,UAAU,EAAA,CAAA;sBADhB,YAAY;uBAAC,UAAU,EAAE,CAAC,QAAQ,CAAC;gBAQ7B,MAAM,EAAA,CAAA;sBADZ,YAAY;uBAAC,MAAM,EAAE,CAAC,QAAQ,CAAC;;;MEjDrB,iBAAiB,CAAA;IACnB,OAAO,GAAG,KAAK;IACf,YAAY,GAAgB,IAAI;IAChC,SAAS,GAAc,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE;IACjD,aAAa,GAAc,CAAC;IAC5B,UAAU,GAAG,KAAK;AAEjB,IAAA,YAAY,GAAG,IAAI,YAAY,EAAQ;AACvC,IAAA,aAAa,GAAG,IAAI,YAAY,EAAa;;AAGhC,IAAA,QAAQ;AACN,IAAA,UAAU;AACX,IAAA,SAAS;AACL,IAAA,aAAa;AACX,IAAA,eAAe;AAChB,IAAA,cAAc;AACjB,IAAA,WAAW;AACT,IAAA,aAAa;AACd,IAAA,YAAY;;IAGvC,MAAM,GAAG,KAAK;AACd,IAAA,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE;AACpC,IAAA,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;IACtC,SAAS,GAAgB,IAAI;IAC7B,mBAAmB,GAAG,KAAK;IAC3B,cAAc,GAAW,EAAE;IAC3B,kBAAkB,GAAqB,OAAO;;IAG9C,QAAQ,GAAG,EAAE;IACb,UAAU,GAAG,EAAE;IACf,SAAS,GAAG,EAAE;;IAGd,aAAa,GAAG,EAAE;IAClB,eAAe,GAAG,EAAE;IACpB,cAAc,GAAG,EAAE;IACnB,WAAW,GAAG,EAAE;IAChB,aAAa,GAAG,EAAE;IAClB,YAAY,GAAG,EAAE;;AAGR,IAAA,UAAU,GAAG;QACpB,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM;QACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE;KACvD;AAEgB,IAAA,YAAY,GAAG;AAC9B,QAAA,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;AACrD,QAAA,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;AACtD,QAAA,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;AACxD,QAAA,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;AACvD,QAAA,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;AACrD,QAAA,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;AACvD,QAAA,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG;KACpD;AAEgB,IAAA,KAAK,GAAG,IAAI,IAAI,EAAE;;IAGnC,KAAK,GAA4B,IAAI;AAC7B,IAAA,QAAQ,GAAG,CAAC,KAAU,KAAI,GAAI;AAC9B,IAAA,SAAS,GAAG,MAAK,GAAI;;AAG7B,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,KAAK,CAAC,GAAG,KAAK;AACjC,YAAA,IAAI,CAAC,aAAa,KAAK,CAAC,GAAG,KAAK,GAAG,OAAO;AAC3D,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,SAA6B,CAAC,CAAC;;AAGzE,IAAA,IAAI,SAAS,GAAA;QACX,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;AAC5C,QAAA,MAAM,KAAK,GAAG,WAAW,GAAG,EAAE,CAAC;AAC/B,QAAA,MAAM,GAAG,GAAG,WAAW,GAAG,EAAE,CAAC;QAC7B,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,GAAG,KAAK,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC;;AAGrE,IAAA,IAAI,YAAY,GAAA;AACd,QAAA,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;AACjE,QAAA,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC;AACpE,QAAA,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,EAAE;;AAGrC,QAAA,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC;AACpC,QAAA,MAAM,SAAS,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;QAC9C,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;;AAGjD,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,GAAG,SAAS,IAAI,CAAC,CAAC;AAC5D,QAAA,MAAM,UAAU,GAAG,WAAW,GAAG,CAAC;QAElC,MAAM,IAAI,GAAkB,EAAE;;AAG9B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;AACnC,YAAA,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC;YAChC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAErC,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,WAAW;YAEvG,IAAI,CAAC,IAAI,CAAC;gBACR,IAAI;gBACJ,cAAc;gBACd,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC;AACzC,gBAAA,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;AACrC,gBAAA,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;AACnC,gBAAA,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AACzC,gBAAA,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI;AACrC,aAAA,CAAC;;AAGJ,QAAA,OAAO,IAAI;;IAGb,QAAQ,GAAA;QACN,IAAI,CAAC,iBAAiB,EAAE;;AAGxB,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;;AAC7E,aAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AAC5B,YAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,YAAY,CAAC;;;AAIvD,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;;IAItB,WAAW,GAAA;;;;AAKX,IAAA,UAAU,CAAC,GAAQ,EAAA;AACjB,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,KAAK,KAAK,SAAS,IAAI,GAAG,CAAC,GAAG,KAAK,SAAS,EAAE;AACtF,gBAAA,IAAI,CAAC,SAAS,GAAG,GAAG;AACpB,gBAAA,IAAI,CAAC,KAAK,GAAG,GAAG;gBAChB,IAAI,CAAC,mCAAmC,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC;;iBACvD;AACL,gBAAA,IAAI,CAAC,SAAS,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE;AAC3C,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI;AACjB,gBAAA,IAAI,CAAC,mCAAmC,CAAC,IAAI,EAAE,IAAI,CAAC;;;aAEjD;AACL,YAAA,IAAI,GAAG,YAAY,IAAI,EAAE;AACvB,gBAAA,IAAI,CAAC,YAAY,GAAG,GAAG;AACvB,gBAAA,IAAI,CAAC,KAAK,GAAG,GAAG;AAChB,gBAAA,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC;;iBAClC;AACL,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI;AACjB,gBAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC;;;;AAK9C,IAAA,gBAAgB,CAAC,EAAO,EAAA;AACtB,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;AAGpB,IAAA,iBAAiB,CAAC,EAAO,EAAA;AACvB,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;AAGrB,IAAA,gBAAgB,CAAE,UAAmB,EAAA;;;;IAKrC,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM;AAC1B,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,iBAAiB,EAAE;;;IAI5B,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;;AAIvB,IAAA,QAAQ,CAAC,SAAiB,EAAA;AACxB,QAAA,IAAI,IAAI,CAAC,kBAAkB,KAAK,OAAO,EAAE;AACvC,YAAA,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;;AACxB,aAAA,IAAI,IAAI,CAAC,kBAAkB,KAAK,MAAM,EAAE;AAC7C,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;;;AAIhC,IAAA,gBAAgB,CAAC,IAAsB,EAAA;AACrC,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI;;IAGhC,kBAAkB,CAAC,KAAoB,EAAE,IAAsB,EAAA;AAC7D,QAAA,QAAQ,KAAK,CAAC,GAAG;AACf,YAAA,KAAK,OAAO;AACZ,YAAA,KAAK,GAAG;gBACN,KAAK,CAAC,cAAc,EAAE;AACtB,gBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;gBAC3B;AACF,YAAA,KAAK,WAAW;gBACd,KAAK,CAAC,cAAc,EAAE;AACtB,gBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AAC3B,gBAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACjB;AACF,YAAA,KAAK,YAAY;gBACf,KAAK,CAAC,cAAc,EAAE;AACtB,gBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AAC3B,gBAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAChB;AACF,YAAA,KAAK,SAAS;gBACZ,KAAK,CAAC,cAAc,EAAE;AACtB,gBAAA,IAAI,IAAI,KAAK,OAAO,EAAE;AACpB,oBAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;AAC9B,oBAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;;qBACZ;AACL,oBAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;AAC7B,oBAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;;gBAEnB;AACF,YAAA,KAAK,WAAW;gBACd,KAAK,CAAC,cAAc,EAAE;AACtB,gBAAA,IAAI,IAAI,KAAK,OAAO,EAAE;AACpB,oBAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;AAC9B,oBAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;;qBACX;AACL,oBAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;AAC7B,oBAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;;gBAElB;AACF,YAAA,KAAK,KAAK;;gBAER;AACF,YAAA;;gBAEE;;;AAIN,IAAA,WAAW,CAAC,SAAiB,EAAA;AAC3B,QAAA,MAAM,MAAM,GAAG,SAAS,GAAG,CAAC,GAAG,MAAM,GAAG,UAAU;AAClD,QAAA,IAAI,IAAI,CAAC,kBAAkB,KAAK,OAAO,EAAE;YACvC,OAAO,CAAA,EAAG,MAAM,CAAA,MAAA,CAAQ;;aACnB;YACL,OAAO,CAAA,EAAG,MAAM,CAAA,KAAA,CAAO;;;AAI3B,IAAA,UAAU,CAAC,IAAU,EAAA;AACnB,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;;aAC1B;AACL,YAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;;;AAIpC,IAAA,UAAU,CAAC,IAAU,EAAA;AACnB,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE;AAC/D,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;;IAIzB,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;;AAIvB,IAAA,cAAc,CAAC,OAAe,EAAA;AAC5B,QAAA,IAAI,CAAC,cAAc,GAAG,OAAO;;AAG/B,IAAA,aAAa,CAAC,OAAe,EAAA;QAC3B,IAAI,CAAC,qBAAqB,EAAE;;IAG9B,SAAS,CAAC,KAAoB,EAAE,OAAe,EAAA;AAC7C,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAA0B;AAC/C,QAAA,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK;;QAG1B,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACpG,YAAA,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,EAAE;gBACzB,IAAI,CAAC,qBAAqB,EAAE;;YAE9B;;;QAIF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YAC3B,KAAK,CAAC,cAAc,EAAE;YACtB;;;QAIF,UAAU,CAAC,MAAK;YACd,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC;SAC7C,EAAE,CAAC,CAAC;;IAGP,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,MAAM,EAAE;;;;AAKjB,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK;AAChC,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAChD,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9B,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;;;AAIhC,IAAA,YAAY,CAAC,KAAU,EAAA;AACrB,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK;AAChC,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAClD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;AAChC,YAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;;;AAIlC,IAAA,WAAW,CAAC,KAAU,EAAA;AACpB,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK;AAChC,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;QACtD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,IAAI,CAAC,qBAAqB,EAAE;;;;AAKhC,IAAA,eAAe,CAAC,KAAU,EAAA;AACxB,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK;AAChC,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACrD,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;AACnC,YAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;;;AAIrC,IAAA,iBAAiB,CAAC,KAAU,EAAA;AAC1B,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK;AAChC,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACvD,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;AACrC,YAAA,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;;;AAIvC,IAAA,gBAAgB,CAAC,KAAU,EAAA;AACzB,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK;AAChC,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;QAC3D,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;;;;AAKtC,IAAA,aAAa,CAAC,KAAU,EAAA;AACtB,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK;AAChC,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACnD,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;AACjC,YAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;;;AAInC,IAAA,eAAe,CAAC,KAAU,EAAA;AACxB,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK;AAChC,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACrD,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;AACnC,YAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;;;AAIrC,IAAA,cAAc,CAAC,KAAU,EAAA;AACvB,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK;AAChC,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;QACzD,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAClC,IAAI,CAAC,qBAAqB,EAAE;;;AAKhC,IAAA,eAAe,CAAC,KAAY,EAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;YAC7C,IAAI,CAAC,KAAK,EAAE;;;;IAKhB,WAAW,CAAC,MAAc,EAAE,GAAgB,EAAA;AAC1C,QAAA,OAAO,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE;;AAGhC,IAAA,aAAa,CAAC,GAAgB,EAAA;QAC5B,MAAM,OAAO,GAAG,EAAE;QAClB,IAAI,CAAC,GAAG,CAAC,cAAc;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;QACpD,IAAI,GAAG,CAAC,OAAO;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QACtC,IAAI,GAAG,CAAC,UAAU;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5C,IAAI,GAAG,CAAC,SAAS;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;QAC3C,IAAI,GAAG,CAAC,YAAY;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;QACjD,IAAI,GAAG,CAAC,UAAU;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;AAC7C,QAAA,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;;AAK1B,IAAA,UAAU,CAAC,IAAiB,EAAA;AAC1B,QAAA,OAAO,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE;;IAG5C,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK;AAAE,YAAA,OAAO,EAAE;AACpC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;QACrD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE;AAC3E,QAAA,OAAO,GAAG,GAAG,CAAG,EAAA,KAAK,CAAM,GAAA,EAAA,GAAG,CAAE,CAAA,GAAG,KAAK;;;AAIlC,IAAA,UAAU,CAAC,KAAa,EAAE,GAAW,EAAE,GAAW,EAAE,SAAiB,EAAA;;QAE3E,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;;QAG7C,MAAM,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC;;QAGrD,MAAM,GAAG,GAAG,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC;QACtC,IAAI,KAAK,CAAC,GAAG,CAAC;AAAE,YAAA,OAAO,EAAE;;QAGzB,IAAI,SAAS,KAAK,CAAC,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAChD,IAAI,GAAG,GAAG,GAAG;gBAAE,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;YACrD,IAAI,GAAG,GAAG,GAAG;gBAAE,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;YACrD,OAAO,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;;;QAItC,IAAI,SAAS,KAAK,CAAC,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAChD,IAAI,GAAG,GAAG,GAAG;AAAE,gBAAA,OAAO,GAAG,CAAC,QAAQ,EAAE;YACpC,IAAI,GAAG,GAAG,GAAG;AAAE,gBAAA,OAAO,GAAG,CAAC,QAAQ,EAAE;;AAGtC,QAAA,OAAO,YAAY;;AAGb,IAAA,gBAAgB,CAAC,cAAsB,EAAA;AAC7C,QAAA,MAAM,UAAU,GAA2D;YACzE,KAAK,EAAE,IAAI,CAAC,UAAU;YACtB,OAAO,EAAE,IAAI,CAAC,SAAS;YACvB,UAAU,EAAE,IAAI,CAAC,eAAe;YAChC,YAAY,EAAE,IAAI,CAAC,cAAc;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,aAAa;YAC5B,UAAU,EAAE,IAAI,CAAC;SAClB;AAED,QAAA,MAAM,SAAS,GAAG,UAAU,CAAC,cAAc,CAAC;QAC5C,IAAI,SAAS,EAAE;YACb,UAAU,CAAC,MAAK;AACd,gBAAA,SAAS,CAAC,aAAa,CAAC,KAAK,EAAE;AAC/B,gBAAA,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE;aACjC,EAAE,CAAC,CAAC;;;IAID,gBAAgB,CAAC,OAAe,EAAE,KAAa,EAAA;AACrD,QAAA,MAAM,UAAU,GAA8B;YAC5C,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC;YAC/B,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC;YAC9C,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;SACxC;QAED,IAAI,KAAK,CAAC,MAAM,IAAI,UAAU,CAAC,OAAO,CAAC,EAAE;AACvC,YAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;;;IAI1B,qBAAqB,GAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,iBAAiB,EAAE;;aACnB;YACL,IAAI,CAAC,kBAAkB,EAAE;;;IAIrB,kBAAkB,GAAA;AACxB,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE;YACtD,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;YACvC,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YAC3C,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;AAEzC,YAAA,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC;AAC3C,YAAA,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE;AAChD,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI;AACjB,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE;AACnC,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE;AACrC,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;AAC5B,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACnB,IAAI,CAAC,SAAS,EAAE;;;;IAKd,iBAAiB,GAAA;;AAEvB,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,cAAc,EAAE;YACrE,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;YACjD,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;YACrD,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;AAEnD,YAAA,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,UAAU,GAAG,CAAC,EAAE,QAAQ,CAAC;AAC/D,YAAA,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,EAAE,UAAU,GAAG,CAAC,EAAE,SAAS,CAAC,EAAE;AACpE,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS;AAChC,gBAAA,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,QAAQ,EAAE;AACxC,gBAAA,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,EAAE;;;;AAK9C,QAAA,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE;YAC/D,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YAC7C,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;YACjD,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;AAE/C,YAAA,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,EAAE,MAAM,CAAC;AACvD,YAAA,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,GAAG,CAAC,EAAE,OAAO,CAAC,EAAE;AAC5D,gBAAA,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,OAAO;;AAG5B,gBAAA,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE;AAC9C,oBAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS;oBAC3B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;AACvC,oBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC;oBAC7B,IAAI,CAAC,SAAS,EAAE;;;;;;AAOhB,IAAA,aAAa,CAAC,SAAiB,EAAA;AACrC,QAAA,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,GAAG,SAAS,EAAE,CAAC,CAAC;AAC5E,QAAA,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,QAAQ,EAAE;AACtC,QAAA,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE;;AAGlC,IAAA,YAAY,CAAC,SAAiB,EAAA;AACpC,QAAA,IAAI,CAAC,WAAW,IAAI,SAAS;;AAE7B,QAAA,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI;AAAE,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI;AACpD,QAAA,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI;AAAE,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;AAG9C,IAAA,qBAAqB,CAAC,IAAU,EAAA;AACtC,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI;AACjB,QAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC;AACxC,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;AAC5B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnB,IAAI,CAAC,SAAS,EAAE;QAChB,IAAI,CAAC,KAAK,EAAE;;AAGN,IAAA,oBAAoB,CAAC,IAAU,EAAA;QACrC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;AACzE,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;;aACnB;AACL,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;;;AAIpB,IAAA,aAAa,CAAC,IAAU,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE;AAC3C,QAAA,IAAI,CAAC,mCAAmC,CAAC,IAAI,EAAE,IAAI,CAAC;AACpD,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;;AAGzB,IAAA,aAAa,CAAC,IAAU,EAAA;AAC9B,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAM;AACnC,QAAA,IAAI,IAAI,GAAG,KAAK,EAAE;AAChB,YAAA,IAAI,CAAC,SAAS,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE;;aACvC;AACL,YAAA,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,IAAI;;AAE3B,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS;AAC3B,QAAA,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QAClF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;AACvC,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC;QAC7B,IAAI,CAAC,SAAS,EAAE;AAChB,QAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK;QAChC,IAAI,CAAC,KAAK,EAAE;;AAGN,IAAA,6BAA6B,CAAC,IAAiB,EAAA;QACrD,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;YAC1D,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;YACnE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE;;aACzC;AACL,YAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;AAClB,YAAA,IAAI,CAAC,UAAU,GAAG,EAAE;AACpB,YAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;;IAIf,mCAAmC,CAAC,SAAsB,EAAE,OAAoB,EAAA;QACtF,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;YACpE,IAAI,CAAC,eAAe,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;YAC7E,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE;;aACnD;AACL,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE;AACvB,YAAA,IAAI,CAAC,eAAe,GAAG,EAAE;AACzB,YAAA,IAAI,CAAC,cAAc,GAAG,EAAE;;QAG1B,IAAI,OAAO,EAAE;AACX,YAAA,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;YAChE,IAAI,CAAC,aAAa,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;YACzE,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE;;aAC/C;AACL,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;AACrB,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE;AACvB,YAAA,IAAI,CAAC,YAAY,GAAG,EAAE;;;IAIlB,iBAAiB,GAAA;AACvB,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;;aAC7E;AACL,YAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,YAAY,CAAC;;;AAIjD,IAAA,SAAS,CAAC,OAAe,EAAA;AAC/B,QAAA,IAAI,CAAC,OAAO;AAAE,YAAA,OAAO,IAAI;QAEzB,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;AAChC,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;AAAE,YAAA,OAAO,IAAI;QAEnC,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1D,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AAAE,YAAA,OAAO,IAAI;AAE/C,QAAA,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC;QAC3C,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI;;AAG3D,IAAA,WAAW,CAAC,IAAU,EAAE,GAAW,EAAE,KAAa,EAAE,IAAY,EAAA;AACtE,QAAA,OAAO,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG;AACtB,YAAA,IAAI,CAAC,QAAQ,EAAE,KAAK,KAAK;AACzB,YAAA,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI;AAC3B,YAAA,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;;AAG7B,IAAA,YAAY,CAAC,IAAU,EAAA;QAC7B,OAAO;AACL,YAAA,IAAI,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;AAC1C,YAAA,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;YACjD,IAAI,CAAC,WAAW;AACjB,SAAA,CAAC,IAAI,CAAC,GAAG,CAAC;;IAGL,SAAS,CAAC,KAAW,EAAE,KAAW,EAAA;QACxC,OAAO,KAAK,CAAC,YAAY,EAAE,KAAK,KAAK,CAAC,YAAY,EAAE;;AAG9C,IAAA,cAAc,CAAC,IAAU,EAAA;QAC/B,OAAO,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY;AAClC,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,KAAK;;AAGhD,IAAA,aAAa,CAAC,IAAU,EAAA;QAC9B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK;AAAE,YAAA,OAAO,KAAK;QAExD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,SAAS;AAChD,QAAA,IAAI,CAAC,GAAG;AAAE,YAAA,OAAO,KAAK;AAEtB,QAAA,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,GAAG;YAC1B,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC;YAC3B,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;AAE1D,QAAA,OAAO,IAAI,GAAG,UAAU,IAAI,IAAI,GAAG,QAAQ;;AAGrC,IAAA,gBAAgB,CAAC,IAAU,EAAA;QACjC,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK;AACpC,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK;;AAGnD,IAAA,cAAc,CAAC,IAAU,EAAA;QAC/B,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG;AAClC,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK;;AAGjD,IAAA,aAAa,CAAC,KAAY,EAAA;AAChC,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAiB;QACtC,OAAO,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,IAAI;;wGAhsBrC,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,iBAAiB,EARjB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAAA,WAAA,EAAA,aAAA,EAAA,eAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,OAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,gBAAA,EAAA,yBAAA,EAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,iBAAiB;AAC1B,gBAAA,WAAW,EAAE,UAAU,CAAC,MAAM,iBAAiB,CAAC;AAChD,gBAAA,KAAK,EAAE;AACR;AACF,SAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,YAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,eAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,aAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,eAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,cAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EChCH,mqQA8KkB,EDxJN,MAAA,EAAA,CAAA,k1JAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,WAAW,0xBAAE,aAAa,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,QAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;4FAYvC,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAd7B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,cAAc,EACf,OAAA,EAAA,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,CAAC,EAGlC,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACpC,SAAA,EAAA;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAE,UAAU,CAAC,uBAAuB,CAAC;AAChD,4BAAA,KAAK,EAAE;AACR;AACF,qBAAA,EAAA,QAAA,EAAA,mqQAAA,EAAA,MAAA,EAAA,CAAA,k1JAAA,CAAA,EAAA;8BAGQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBAES,YAAY,EAAA,CAAA;sBAArB;gBACS,aAAa,EAAA,CAAA;sBAAtB;gBAGsB,QAAQ,EAAA,CAAA;sBAA9B,SAAS;uBAAC,UAAU;gBACI,UAAU,EAAA,CAAA;sBAAlC,SAAS;uBAAC,YAAY;gBACC,SAAS,EAAA,CAAA;sBAAhC,SAAS;uBAAC,WAAW;gBACM,aAAa,EAAA,CAAA;sBAAxC,SAAS;uBAAC,eAAe;gBACI,eAAe,EAAA,CAAA;sBAA5C,SAAS;uBAAC,iBAAiB;gBACC,cAAc,EAAA,CAAA;sBAA1C,SAAS;uBAAC,gBAAgB;gBACD,WAAW,EAAA,CAAA;sBAApC,SAAS;uBAAC,aAAa;gBACI,aAAa,EAAA,CAAA;sBAAxC,SAAS;uBAAC,eAAe;gBACC,YAAY,EAAA,CAAA;sBAAtC,SAAS;uBAAC,cAAc;gBAiXzB,eAAe,EAAA,CAAA;sBADd,YAAY;uBAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC;;;MEnY/B,uBAAuB,CAAA;AAmBd,IAAA,UAAA;AAlBX,IAAA,OAAO,GAAuB;QACrC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE;QAC5D,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,OAAO,EAAE;QAC5D,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK;KAC/C;IAEQ,QAAQ,GAAG,WAAW;IACtB,iBAAiB,GAAG,KAAK;IACzB,QAAQ,GAAG,aAAa;IACxB,YAAY,GAAqB,OAAO;IACxC,QAAQ,GAAG,EAAE;AAEZ,IAAA,cAAc,GAAG,IAAI,YAAY,EAAoB;AAE/D,IAAA,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC;AACzB,IAAA,cAAc,GAAG,MAAM,CAAC,KAAK,CAAC;IACtB,mBAAmB,GAAG,KAAK;AAEnC,IAAA,WAAA,CAAoB,UAAsB,EAAA;QAAtB,IAAU,CAAA,UAAA,GAAV,UAAU;;AAG9B,IAAA,eAAe,CAAC,KAAiB,EAAA;AAC/B,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YACzD,IAAI,CAAC,aAAa,EAAE;;;IAIxB,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;;IAG1B,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC;;QAEzB,UAAU,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC7B,IAAI,CAAC,aAAa,EAAE;;SAEvB,EAAE,GAAG,CAAC;;IAGT,oBAAoB,GAAA;AAClB,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;;IAGjC,oBAAoB,GAAA;AAClB,QAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK;QAChC,IAAI,CAAC,aAAa,EAAE;;AAGtB,IAAA,cAAc,CAAC,KAAY,EAAA;QACzB,KAAK,CAAC,eAAe,EAAE;AACvB,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC;;IAG7C,YAAY,CAAC,MAAwB,EAAE,KAAY,EAAA;QACjD,KAAK,CAAC,eAAe,EAAE;AACvB,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;QAChC,IAAI,CAAC,aAAa,EAAE;;IAGd,aAAa,GAAA;AACnB,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC;AAC9B,QAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC;;AAG3B,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,YAAY,KAAK,MAAM,GAAG,OAAO,GAAG,MAAM;;IAGxD,kBAAkB,CAAC,KAAa,EAAE,MAAwB,EAAA;QACxD,OAAO,MAAM,CAAC,KAAK;;AAGrB,IAAA,YAAY,CAAC,MAAyB,EAAA;QACpC,IAAI,MAAM,EAAE;AACV,YAAA,OAAO,MAAM,CAAC,aAAa,IAAI,KAAK;;QAEtC,OAAO,IAAI,CAAC,iBAAiB;;wGA/EpB,uBAAuB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,uBAAuB,EClCpC,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,gBAAA,EAAA,yBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,kBAAA,EAAA,2BAAA,EAAA,mBAAA,EAAA,4BAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,0+EAgFM,EDxDM,MAAA,EAAA,CAAA,whIAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,aAAa,0JAAE,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;4FAU1B,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAZnC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,sBAAsB,EACvB,OAAA,EAAA,CAAC,aAAa,EAAE,YAAY,CAAC,EAAA,eAAA,EAGrB,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAC/B,IAAA,EAAA;AACJ,wBAAA,oBAAoB,EAAE,yBAAyB;AAC/C,wBAAA,qBAAqB,EAAE;AACxB,qBAAA,EAAA,QAAA,EAAA,0+EAAA,EAAA,MAAA,EAAA,CAAA,whIAAA,CAAA,EAAA;+EAGQ,OAAO,EAAA,CAAA;sBAAf;gBAMQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,iBAAiB,EAAA,CAAA;sBAAzB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBAES,cAAc,EAAA,CAAA;sBAAvB;gBASD,eAAe,EAAA,CAAA;sBADd,YAAY;uBAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC;;;AEvD5C;MAqBa,eAAe,CAAA;AAClB,IAAA,cAAc,GAAG,MAAM,CAAsB,IAAI,CAAC;AACjD,IAAA,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE;AAErD,IAAA,IAAI,CACF,OAAe,EACf,QAA6B,GAAA,eAAe,EAC5C,QAAQ,GAAG,IAAI,EACf,KAAK,GAAG,MAAM,EACd,eAAe,GAAG,SAAS,EAAA;AAE3B,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;YACtB,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,KAAK;YACL;AACD,SAAA,CAAC;;QAGF,UAAU,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC;SAC9B,EAAE,QAAQ,CAAC;;wGAtBH,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAf,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,cADF,MAAM,EAAA,CAAA;;4FACnB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,UAAU;mBAAC,EAAE,UAAU,EAAE,MAAM,EAAE;;;MCTrB,iBAAiB,CAAA;AAC5B,IAAA,eAAe,GAAG,MAAM,CAAC,eAAe,CAAC;;AAGzC,IAAA,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS;wGAJ/B,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;4FAAjB,iBAAiB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECX9B,gQAKM,EAAA,MAAA,EAAA,CAAA,8eAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EDEM,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,CAAA;;4FAIX,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAP7B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,cAAc,EACZ,UAAA,EAAA,IAAI,EACP,OAAA,EAAA,CAAC,YAAY,CAAC,EAAA,QAAA,EAAA,gQAAA,EAAA,MAAA,EAAA,CAAA,8eAAA,CAAA,EAAA;;;AEPzB;;AAEG;AAEH;AAuCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDE;;AC5FF;;AAEG;;;;"}