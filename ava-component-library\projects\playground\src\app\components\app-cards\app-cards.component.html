<div class="pcard-wrapper documentation ">


  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Cards Component</h1>
        <p class="description">
          A versatile card component that supports multiple sizes, themes,
          animations, and flexible content structure. Designed for creating
          visually appealing and interactive card layouts.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} CardsComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!---Section -->
  <div class="doc-sections">
    <section class="doc-section">
      <div class="row">
        <div class="section-header" tabindex="0" role="button">
          <h2>Basic Card</h2>
          <p> Cards are designed to highlight key features with prominent visuals.</p>
        </div>


      </div>
      <div class="code-example">
        <div class="example-preview">
          <div class="row g-3">
            <div class="col-12 col-sm-auto">
              <ava-card>
                <div header>
                  <h3>Card Header 1</h3>
                </div>
                <div content>
                  <p>
                    Lorem ipsum dolor sit <br />adipisicing elit. Labore,
                    totam velit? Iure nemo ?
                  </p>
                </div>
                <div footer>
                  <ava-button label="Read More" variant="primary" size="medium" state="default"></ava-button>
                </div>
              </ava-card>
            </div>
            <div class="col-12 col-sm-auto">
              <ava-card>
                <div header>
                  <h3>Card Header 1</h3>
                </div>
                <div content>
                  <p>
                    Lorem ipsum dolor sit <br />adipisicing elit. Labore,
                    totam velit? Iure nemo ?
                  </p>
                </div>
                <div footer>
                  <ava-button label="Read More" variant="primary" size="medium" state="default"></ava-button>
                </div>
              </ava-card>
            </div>

          </div>
        </div>
      </div>


    </section>
  </div>

  <div class="doc-sections">
    <section class="doc-section">
      <div class="row">
        <div class="section-header" tabindex="0" role="button">
          <h2>Feature Card</h2>
          <p> Cards are designed to highlight key features with prominent visuals.</p>
        </div>


      </div>
      <div class="code-example">
        <div class="example-preview fe-card">
          <div class="row g-3">
            <div class="col-12 col-md-4">
              <ava-feature-card>
                <div header>
                  <h3>Blue Varient Card</h3>
                </div>
                <div content>
                  <p>
                    Lorem ipsum dolor sit <br />adipisicing elit. Labore,
                    totam velit? Iure nemo ?
                  </p>
                </div>
                <div footer>
                  <ava-button label="Read more" variant="primary" visual="glass" height="40"
                    state="default"></ava-button>
                </div>
              </ava-feature-card>
            </div>
            <div class="col-12 col-md-4 ">
              <ava-feature-card [variant]="'red'">
                <div header>
                  <h3>Red Varient Card</h3>
                </div>
                <div content>
                  <p>
                    Lorem ipsum dolor sit <br />adipisicing elit. Labore,
                    totam velit? Iure nemo ?
                  </p>
                </div>
                <div footer>
                  <ava-button label="Read more" variant="primary" visual="glass" height="40"
                    state="default"></ava-button>
                </div>
              </ava-feature-card>
            </div>
            <div class="col-12 col-md-4 ">
              <ava-feature-card [variant]="'green'">
                <div header>
                  <h3>Green Varient Card</h3>
                </div>
                <div content>
                  <p>
                    Lorem ipsum dolor sit <br />adipisicing elit. Labore,
                    totam velit? Iure nemo ?
                  </p>
                </div>
                <div footer>
                  <ava-button label="Read more" variant="primary" visual="glass" height="40"
                    state="default"></ava-button>
                </div>
              </ava-feature-card>
            </div>
          </div>

        </div>
        <div class="example-preview fe-card">
          <div class="row g-3">
            <div class="col-12 col-md-4 ">
              <ava-feature-card [variant]="'purple'">
                <div header>
                  <h3>Purple Varient Card</h3>
                </div>
                <div content>
                  <p>
                    Lorem ipsum dolor sit <br />adipisicing elit. Labore,
                    totam velit? Iure nemo ?
                  </p>
                </div>
                <div footer>
                  <ava-button label="Read more" variant="primary" visual="glass" height="40"
                    state="default"></ava-button>
                </div>
              </ava-feature-card>
            </div>
            <div class="col-12 col-md-4 ">
              <ava-feature-card [variant]="'orange'">
                <div header>
                  <h3>Orange Varient Card</h3>
                </div>
                <div content>
                  <p>
                    Lorem ipsum dolor sit <br />adipisicing elit. Labore,
                    totam velit? Iure nemo ?
                  </p>
                </div>
                <div footer>
                  <ava-button label="Read more" variant="primary" visual="glass" height="40"
                    state="default"></ava-button>
                </div>
              </ava-feature-card>
            </div>
            <div class="col-12 col-md-4 ">
              <ava-feature-card [variant]="'teal'">
                <div header>
                  <h3>Teal Varient Card</h3>
                </div>
                <div content>
                  <p>
                    Lorem ipsum dolor sit <br />adipisicing elit. Labore,
                    totam velit? Iure nemo ?
                  </p>
                </div>
                <div footer>
                  <ava-button label="Read more" variant="primary" visual="glass" height="40"
                    state="default"></ava-button>
                </div>
              </ava-feature-card>
            </div>
          </div>
        </div>
      </div>


    </section>
  </div>
  <!-- 

  <div class="doc-sections">
    <section class="doc-section">
      <div class="row">
        <div class="section-header" tabindex="0" role="button">
          <h2>Advanced Card</h2>
          <p>
            Lorem ipsum dolor sit <br />adipisicing elit. Labore,
            totam velit? Iure nemo dolor ?
          </p>
        </div>
      </div>
      <div class="code-example">
        <div class="example-preview ad-card">
          <div class="row g-3">
            <div class="col-12 col-sm-auto">
              <ava-advanced-card>
                <div header>
                  <h3>Advanced Header 1</h3>
                </div>
                <div content>
                  <p>
                    Lorem ipsum dolor sit <br />adipisicing elit. Labore,
                    totam velit? Iure nemo ?
                  </p>
                </div>
                <div footer>
                  <ava-button label="Read More" variant="primary" size="medium" state="default"
                    visual='glass'></ava-button>
                </div>
              </ava-advanced-card>
            </div>
            <div class="col-12 col-sm-auto">
              <ava-advanced-card>
                <div header>
                  <h3>Advanced Header 2</h3>
                </div>
                <div content>
                  <p>
                    Lorem ipsum dolor sit <br />adipisicing elit. Labore,
                    totam velit? Iure nemo ?
                  </p>
                </div>
                <div footer>
                  <ava-button label="Read More" variant="primary" size="medium" state="default"
                    visual='glass'></ava-button>
                </div>
              </ava-advanced-card>
            </div>
          </div>
        </div>
      </div>


    </section>
  </div> -->


  <div class="doc-sections">
    <section class="doc-section">
      <div class="row">
        <div class="section-header" tabindex="0" role="button">
          <h2>Console Card</h2>

        </div>
      </div>
      <div class="code-example">
        <div class="example-preview ">
          <div class="row g-3">
            <div class="col-12 col-sm-auto">
              <ng-template let-i="index" let-label="label" #footerTemplate>
                <div class="footer-content">
                  <ng-container *ngIf="label">
                    <div class="footer-left">
                      <ava-icon iconSize="20" [iconName]="label.iconName"></ava-icon>
                      <span>{{label.status}}</span>
                    </div>
                  </ng-container>
                  <div class="footer-right">
                    <ava-button label="Test" (userClick)="uClick(i)" variant="secondary" size="medium" state="default"
                      iconName="play" iconPosition="left"></ava-button>
                    <ava-button label="Sendback" (userClick)="uClick(i)" variant="secondary" size="medium"
                      state="default" iconName="move-left" iconPosition="left"></ava-button>
                    <ava-button label="Approve" (userClick)="uClick(i)" variant="primary" size="medium" state="default"
                      iconName="Check" iconPosition="left"></ava-button>
                  </div>
                </div>
              </ng-template>
              <ava-approval-card height="300" [contentTemplate]="footerTemplate" [cardData]="consoleApproval"
                [contentsBackground]="'#ffffff'" [cardContainerBackground]="'#F8F8F8'">
              </ava-approval-card>
            </div>

          </div>
        </div>
      </div>


    </section>
  </div>


  <!-- 
  <div class="doc-sections">
    <section class="doc-section">
      <div class="row">
        <div class="section-header" tabindex="0" role="button">
          <h2>Image Card</h2>

        </div>
      </div>
      <div class="code-example">
        <div class="example-preview">
          <div class="row g-3">
            <div class="col-12 col-sm-auto img-card">

              <ava-image-card [imageUrl]="'assets/robot.png'" [name]="'Shouvik Mazumdar'" [title]="'Welcome, User 🚀'">
              </ava-image-card>

            </div>
            <div class="col-12 col-sm-auto img-card">

              <ava-image-card [imageUrl]="'assets/robot.png'" [name]="'Shouvik Mazumdar'" [title]="'Welcome, User 🚀'">
              </ava-image-card>

            </div>

          </div>
        </div>
      </div>


    </section>
  </div> -->

  <div class="doc-sections">
    <section class="doc-section">
      <div class="row">
        <div class="section-header" tabindex="0" role="button">
          <h2>Ava Text Card</h2>
          <p>
            Lorem ipsum dolor sit <br />adipisicing elit. Labore,
            totam velit? Iure nemo dolor ?
          </p>
        </div>
      </div>
      <div class="code-example">
        <div class="example-preview-text fe-card text-card">
          <div class="row g-3">
            <div class="col-12 col-md-4 ">
              <ava-text-card variant="blue" [iconName]="'trending-up'" [title]="'Active Workflows'" [value]="70"
                [description]="'Agents actively running'" [iconName]="'trending-up'">
              </ava-text-card>

            </div>
            <div class="col-12 col-md-4 ">
              <ava-text-card variant="purple" [iconName]="'trending-up'" [title]="'Active Active'" [value]="1240"
                [description]="'Agents active'" [iconName]="'trending-up'">
              </ava-text-card>
            </div>
            <div class="col-12 col-md-4  ">
              <ava-text-card variant="green" [iconName]="'trending-up'" [title]="'Agents Approvals'" [value]="120"
                [description]="'Agents actively running'" [iconName]="'trending-up'">
              </ava-text-card>
            </div>


          </div>
        </div>
      </div>


    </section>
  </div>





</div>