import { ChangeDetectionStrategy, Component, Input, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../components/icon/icon.component';
import { FeatureCardComponent } from '../../components/feature-card/feature-card.component';

@Component({
  selector: 'ava-text-card',
  standalone: true,
  imports: [CommonModule, IconComponent, FeatureCardComponent],
  templateUrl: './text-card.component.html',
  styleUrls: ['./text-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class TextCardComponent {
  @Input() variant: 'blue' | 'red' | 'purple' | 'green' | 'orange' | 'teal' = 'blue';
  @Input() iconName = 'trending-up';
  @Input() title: string = '';
  @Input() value: string | number = '';
  @Input() description: string = '';
  @Input() width = 0;
}
